package com.lenskart.nexs.grn.service;

import java.util.List;
import java.util.Map;

import com.lenskart.nexs.common.entity.po.grn.GrnItemEntity;
import com.lenskart.nexs.common.model.response.grn.BarcodePriceResponse;
import com.lenskart.nexs.exception.CustomException;
import com.lenskart.nexs.grn.enums.ActionStatus;
import com.lenskart.nexs.grn.model.GRNItem;

import com.lenskart.nexs.ims.request.BarcodePriceDetailsRequest;
import com.lenskart.nexs.ims.request.FetchStockDetailsRequest;
import com.lenskart.nexs.ims.request.StockRequestV2;
import com.lenskart.nexs.ims.request.UpdateStockInwardRequest;
import com.lenskart.nexs.ims.request.UpdateStocksRequestV2;
import com.lenskart.nexs.ims.response.FetchBarcodeInwardResponse;
import com.lenskart.nexs.ims.response.FetchStockDetailsResponse;
import com.lenskart.nexs.ims.response.UpdateStocksResponseV2;

public interface IMSService {

	UpdateStocksRequestV2 frameUpdateStockRequestForGRNItem(GRNItem grnItem, ActionStatus operation, String location,
			String imsOperation);

	StockRequestV2 getStockRequestV2(GRNItem grnItem, String location);

//	UpdateStocksResponseV2 processStockUpdateRequest(UpdateStocksRequestV2 updateStocksRequestV2)
//			throws CustomException;

	void updateImsItemStatus(String grnItem, String imsIqcGrnHold, String iqc);

	UpdateStocksRequestV2 frameUpdateStockRequestForIqcGRNItem(List<GrnItemEntity> grnItemsList, ActionStatus operation,
			String location, String grnIqcStatus);

	FetchStockDetailsResponse fetchStockDetails(FetchStockDetailsRequest request) throws Exception;

	UpdateStocksResponseV2 validateStockUpdateRequest(UpdateStocksRequestV2 updateStocksRequestV2);

//	public UpdateStocksResponseV2 processStockUpdateRequestSync(UpdateStocksRequestV2 updateStocksRequestV2);

	FetchBarcodeInwardResponse fetchBarcodePriceDetails(BarcodePriceDetailsRequest barcodePriceDetailsRequest) throws CustomException;

	UpdateStockInwardRequest frameUpdateStockRequestForGRNItemV2(GrnItemEntity grnItemDetails, String imsOperation);

	UpdateStockInwardRequest frameUpdateStockRequestForIqcGRNItemV2(List<GrnItemEntity> grnItemsList,
			ActionStatus operation, String location, String grnIqcStatus) throws Exception;

    UpdateStocksResponseV2 performStockInward(UpdateStockInwardRequest updateStockInwardRequest);

	void updateLandedCostDetails(String poId, String pId) throws Exception;

	UpdateStockInwardRequest createUpdateStockInwardRequest(List<GrnItemEntity> grnItems, String imsOperation,
															Map<String, BarcodePriceResponse> barcodePriceResponse, String currency, Map<String, String> barcodeUrlMap) throws CustomException;

}
