package com.lenskart.nexs.grn.service.strategy.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.baseResponse.BaseResponseModel;
import com.lenskart.nexs.common.entity.entityService.PurchaseOrderEntityService;
import com.lenskart.nexs.common.entity.entityService.PurchaseOrderItemEntityService;
import com.lenskart.nexs.common.entity.po.PurchaseOrder;
import com.lenskart.nexs.common.entity.po.PurchaseOrderItem;
import com.lenskart.nexs.common.entity.po.grn.GrnItemEntity;
import com.lenskart.nexs.common.model.response.grn.BarcodePriceResponse;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.constants.RedisOps;
import com.lenskart.nexs.exception.CustomException;
import com.lenskart.nexs.fms.model.entity.LegalOwner;
import com.lenskart.nexs.fms.model.repo.LegalOwnerConfigRepository;
import com.lenskart.nexs.grn.enums.ActionStatus;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.GRNItem;
import com.lenskart.nexs.grn.service.IMSService;
import com.lenskart.nexs.grn.util.RetryUtils;
import com.lenskart.nexs.service.RedisHandler;

import com.lenskart.nexs.ims.connector.IMSConnector;
import com.lenskart.nexs.ims.enums.SourceOperations;
import com.lenskart.nexs.ims.request.BarcodePriceDetailsRequest;
import com.lenskart.nexs.ims.request.FetchStockDetailsRequest;
import com.lenskart.nexs.ims.request.StockInwardRequest;
import com.lenskart.nexs.ims.request.StockRequestV2;
import com.lenskart.nexs.ims.request.UpdateStockInwardRequest;
import com.lenskart.nexs.ims.request.UpdateStocksRequestV2;
import com.lenskart.nexs.ims.response.FetchBarcodeInwardResponse;
import com.lenskart.nexs.ims.response.FetchStockDetailsResponse;
import com.lenskart.nexs.ims.response.UpdateStocksResponseV2;

@Service
public class IMSServiceImpl implements IMSService {

	@CustomLogger
    private Logger log;

    @Value("${ims.base.url}")
    private String imsBaseURL;

//    @Value("${ims.stockInAndOutV2.url}")
//    private String imsStockInAndOutV2;

    @Value("${nexs.ims.fetchBarcodeItemDetails.url}")
    private String fetchBarcodeItemDetailsUrl;

    @Value("${nexs.ims.response.size}")
    private int responseSize;
    
    @Value("${ims.stockInwardUpdate.url:/nexs/api/ims/stockInwardUpdate}")
    private String stockInwardUpdateUrl;

    @Value("${ims.validateStockInAndOutV2.url:/nexs/api/ims/validate/barcode}")
    private String validateStockInAndOutV2;

    @Value("${nexs.ims.stockUpdateV2.api.retry.count:3}")
    private int nexsImsStockUpdateV2ApiRetryCount;

    @Value("${nexs.ims.barcode.price.details.api.timeout:30000}")
    private int nexsImsPriceDetailsApiTimeout;

    @Value("${nexs.ims.barcode.price.details.endpoint:/nexs/api/ims/fetchBarcodePrice}")
    private String imsFetchBarcodePriceDetails;

    @Autowired
    private PurchaseOrderItemEntityService purchaseOrderItemEntityService;

    @Autowired
    private PurchaseOrderEntityService purchaseOrderEntityService;

    @Autowired
    private LegalOwnerConfigRepository legalOwnerConfigRepository;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public UpdateStocksRequestV2 frameUpdateStockRequestForGRNItem(GRNItem grnItem, ActionStatus operation,
                                                                   String location, String imsOperation) {
        try {
            log.info("Frame update stock request for GRN item barcode: {}, operation {}",
                    grnItem.getBarcode(), operation.getName());
            UpdateStocksRequestV2 updateStocksRequest = new UpdateStocksRequestV2();

            List<StockRequestV2> stockRequestList = new ArrayList<>();
            StockRequestV2 stockRequest = getStockRequestV2(grnItem, location);
            stockRequestList.add(stockRequest);

            updateStocksRequest.setStockRequestV2List(stockRequestList);
            updateStocksRequest.setDeleteBarcode(operation.equals(ActionStatus.IMS_DELETE_GRN_ITEMS));
            updateStocksRequest.setOperation(imsOperation);
            log.info("Frame update stock request for GRN item barcode: {}, updateStocksRequest {}",
                    grnItem.getBarcode(), updateStocksRequest);

            return updateStocksRequest;
        } catch (Exception e) {
            log.error("Error: Frame update stock request for GRN item {}, operation {}, error {}",
                    grnItem, operation.getName(), e.getMessage());
            throw new ApplicationException("Error: Frame update stock request for GRN item " + e.getMessage(),
                    GRNExceptionStatus.GRN_BAD_REQUEST);
        }
    }

    @Override
    public StockRequestV2 getStockRequestV2(GRNItem grnItem, String location) {
        StockRequestV2 stockRequest = new StockRequestV2();
        stockRequest.setBarcode(grnItem.getBarcode());
        stockRequest.setExpiry(grnItem.getExpiryDate());
        stockRequest.setFacility(grnItem.getFacility());
        stockRequest.setPid(Integer.valueOf(grnItem.getPid()));
        stockRequest.setUpdatedBy(grnItem.getUpdatedBy());
        stockRequest.setLegalOwner(grnItem.getLegalOwner());
        if (StringUtils.isEmpty(grnItem.getQcFailBoxBarcode()))
            stockRequest.setBoxBarcode(grnItem.getQcPassBoxBarcode());
        else
            stockRequest.setBoxBarcode(grnItem.getQcFailBoxBarcode());
        if (grnItem.getId() != null) {
        	stockRequest.setActionId(grnItem.getId().toString());
        } else {
        	stockRequest.setActionId(grnItem.getBarcode());
        }
        stockRequest.setLocation(location);
        return stockRequest;
    }

//    @SuppressWarnings("unused")
//	@Override
//    public UpdateStocksResponseV2 processStockUpdateRequest(UpdateStocksRequestV2 updateStocksRequestV2) throws CustomException {
//        UpdateStocksResponseV2 updateStocksResponseV2 = null;
//        for (int retryCount = 0; retryCount < nexsImsStockUpdateV2ApiRetryCount; retryCount++) {
//        	try {
//                String url = imsBaseURL + imsStockInAndOutV2;
//        		log.info("Send request to ims url {}, for update stock {}", url, updateStocksRequestV2);
//        		updateStocksResponseV2 = IMSConnector.updateIMSV2(updateStocksRequestV2, url);
//        		log.info("Response request to ims for update stock request {} : {} ", updateStocksRequestV2, updateStocksResponseV2.toString());
//        		break;
//        	} catch (Exception ex) {
//        		log.error("Error: Send request to ims for update stock {}, {}, retryCount {}", updateStocksRequestV2,
//        				ex.getMessage(), retryCount);
//                throw new CustomException(String.format("Error: Send request to ims for update stock {}, {}, retryCount {}", updateStocksRequestV2,
//                        ex.getMessage(), retryCount), 400);
//        	}
//        }
//        return updateStocksResponseV2;
//    }

    @Override
    public void updateImsItemStatus(String grnCode, String operation, String iqc) {
        try {
            log.info("Send request to ims for IQC status {} with grnCode code: {} ", operation, grnCode);
            log.info("Process IQC status URL: {}", imsBaseURL);
            // UpdateStocksResponseV2 imsIqcStatusResponse = IMSConnector.updateIMSIqcStatus(imsIqcGrnComplete, imsBaseURL);
            log.info("Response request to ims for IQC status {} with grnCode code: {} ", operation, grnCode);
        } catch (Exception e) {
            throw new ApplicationException("Error: Send request to ims for update IQC status " + e.getMessage(),
                    GRNExceptionStatus.GRN_BAD_REQUEST);
        }
    }

    @Override
    public UpdateStocksRequestV2 frameUpdateStockRequestForIqcGRNItem(List<GrnItemEntity> grnItemsList,
                                                                      ActionStatus operation, String location,
                                                                      String imsOperation) {
        try {
            UpdateStocksRequestV2 updateStocksRequest = new UpdateStocksRequestV2();

            List<StockRequestV2> stockRequestList = new ArrayList<>();
            for (GrnItemEntity grnItem : grnItemsList) {
                StockRequestV2 stockRequest = new StockRequestV2();
                stockRequest.setBarcode(grnItem.getBarcode());
                stockRequest.setPid(Integer.valueOf(grnItem.getPid()));
                stockRequest.setFacility(grnItem.getFacility());
//                stockRequest.setVendorCode(grnItem.getVendorCode());
//                stockRequest.setPutAwayCode(String.valueOf(grnItem.getPutawayCode()));
                stockRequest.setUpdatedBy(grnItem.getUpdatedBy());
                stockRequest.setOrderId(null);
                stockRequest.setLocation(location);
                stockRequest.setExpiry(grnItem.getExpiryDate());
                stockRequest.setBoxBarcode(grnItem.getQcPassBoxBarcode());
                stockRequest.setLegalOwner(grnItem.getLegalOwner());
                stockRequestList.add(stockRequest);
                updateStocksRequest.setStockRequestV2List(stockRequestList);
            }
            updateStocksRequest.setOperation(imsOperation);

            return updateStocksRequest;
        } catch (Exception e) {
            log.error("Error: Frame update stock request for , operation {}, error {}",
                    operation.getName(), e.getMessage());
            throw new ApplicationException("Error: Frame update stock request for GRN item " + e.getMessage(),
                    GRNExceptionStatus.GRN_BAD_REQUEST);
        }
    }

    @Override
    @Logging
    public FetchStockDetailsResponse fetchStockDetails(FetchStockDetailsRequest request) throws Exception {
        String url = imsBaseURL + fetchBarcodeItemDetailsUrl;
        log.info("FetchStockDetailsResponse Fetching details from IMS for url {} barcode request {}",
                url, request.toString());

        FetchStockDetailsResponse fetchStockDetailsResponse = new FetchStockDetailsResponse();
        try {
            String response = RetryUtils.postData(url + "?size=" + responseSize, request, String.class);
            objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL).configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            BaseResponseModel baseResponseModel = objectMapper.readValue(response, BaseResponseModel.class);
            log.info("FetchStockDetailsResponse response: {}", baseResponseModel.getData());
            if (baseResponseModel.getData() != null) {
                fetchStockDetailsResponse = objectMapper.convertValue(
                        baseResponseModel.getData(), new TypeReference<FetchStockDetailsResponse>() {
                        });
            }
            log.info("FetchStockDetailsResponse fetchStockDetailsResponse: {}", fetchStockDetailsResponse);
            return fetchStockDetailsResponse;
        } catch (Exception e) {
            log.error("[fetchStockDetails] error " + e.getMessage(), e);
            throw new Exception("Fetch location barcode from ims failed ,response is " + e.getMessage());
        }
    }

    @Override
    public UpdateStocksResponseV2 validateStockUpdateRequest(UpdateStocksRequestV2 updateStocksRequestV2) {
        try {
            log.info("[validateStockUpdateRequest] Send request to ims for validate stock {} with inventory type: {} ",
                    updateStocksRequestV2, updateStocksRequestV2.getOperation());
            log.info("[validateStockUpdateRequest] Process stock update URL: {}", imsBaseURL + validateStockInAndOutV2);
            UpdateStocksResponseV2 updateStocksResponseV2 = IMSConnector.updateIMSV2(updateStocksRequestV2, imsBaseURL + validateStockInAndOutV2);
            log.info("[validateStockUpdateRequest] Response request to ims for validate stock {} with inventory type: {} ",
                    updateStocksResponseV2.toString(), updateStocksRequestV2.getOperation());
            return updateStocksResponseV2;
        } catch (Exception e) {
            log.error("[validateStockUpdateRequest] Error: Send request to ims for validate stock {}, {}",
                    updateStocksRequestV2, e.getMessage());
            throw new ApplicationException("[validateStockUpdateRequest] Unable to validate stock update request " + e.getMessage(),
                    GRNExceptionStatus.GRN_BAD_REQUEST);
        }
    }

//    @Override
//    public UpdateStocksResponseV2 processStockUpdateRequestSync(UpdateStocksRequestV2 updateStocksRequestV2) {
//        try {
//            log.info("Send request to ims for update stock {} with inventory type: {} ",
//                    updateStocksRequestV2, updateStocksRequestV2.getOperation());
//            log.info("Process stock update URL: {}", imsBaseURL + imsStockInAndOutV2);
//            UpdateStocksResponseV2 updateStocksResponseV2 = IMSConnector.updateIMSV2(updateStocksRequestV2, imsBaseURL + imsStockInAndOutV2);
//            log.info("Response request to ims for update stock {} with inventory type: {} ",
//                    updateStocksResponseV2.toString(), updateStocksRequestV2.getOperation());
//            return updateStocksResponseV2;
//        } catch (Exception e) {
//            log.error("Error: Send request to ims for update stock {}, {}",
//                    updateStocksRequestV2, e.getMessage());
//            throw new ApplicationException("Error: Send request to ims for update stock " + e.getMessage(),
//                    GRNExceptionStatus.GRN_BAD_REQUEST);
//        }
//    }

	@Override
	public FetchBarcodeInwardResponse fetchBarcodePriceDetails(BarcodePriceDetailsRequest barcodePriceDetailsRequest)
			throws CustomException {
		try {
			String url = imsBaseURL + imsFetchBarcodePriceDetails;
			log.info("Send request to ims url {}, to fetch barcode price details {}", url, barcodePriceDetailsRequest);
			FetchBarcodeInwardResponse fetchBarcodePriceDetails = IMSConnector.fetchBarcodePriceDetails(url,
					barcodePriceDetailsRequest, nexsImsPriceDetailsApiTimeout);
			log.info("Response from IMS fetch barcode price {} : {} ", barcodePriceDetailsRequest,
					fetchBarcodePriceDetails);
			return fetchBarcodePriceDetails;
		} catch (Exception ex) {
			log.error("Error: Send request to ims for barcode price {}, {}, retryCount {}", barcodePriceDetailsRequest,
					ex.getMessage(), nexsImsPriceDetailsApiTimeout);
			throw new CustomException(
					String.format("Error: Send request to ims for barcode price {}, {}, retryCount {}",
							barcodePriceDetailsRequest, ex.getMessage(), nexsImsPriceDetailsApiTimeout), 400);
		}
	}

    public UpdateStockInwardRequest frameUpdateStockRequestForGRNItemV2(GrnItemEntity grnItem, String imsOperation) {
        try {
            log.info("[frameUpdateStockRequestForGRNItemV2] Frame update stock request for GRN item barcode: {}, operation {}",
                    grnItem.getBarcode(), imsOperation);
            UpdateStockInwardRequest updateStockInwardRequest = new UpdateStockInwardRequest();
            List<StockInwardRequest> stockInwardRequestItems = new ArrayList<>();
            StockInwardRequest inwardRequestItem = prepareStockInwardRequest(grnItem, getIMSLocation(grnItem), null, null);
            log.info("[frameUpdateStockRequestForGRNItemV2] Frame update stock request for GRN item barcode: {}, operation {}, inwardRequestItem {}",
                    grnItem.getBarcode(), imsOperation, inwardRequestItem);
            stockInwardRequestItems.add(inwardRequestItem);
            updateStockInwardRequest.setStockInwardRequestList(stockInwardRequestItems);
            updateStockInwardRequest.setOperation(imsOperation);
            updateStockInwardRequest.setUpdatedBy(grnItem.getUpdatedBy());
            log.info("[frameUpdateStockRequestForGRNItemV2] Frame update stock request for GRN item barcode: {}, updateStockInwardRequest {}",
                    grnItem.getBarcode(), updateStockInwardRequest);
            return updateStockInwardRequest;
        } catch (Exception e) {
            log.error("[frameUpdateStockRequestForGRNItemV2] Error GRN item {}, operation {}, error {}",
            		grnItem, imsOperation, e.getMessage());
            throw new ApplicationException("Error: Frame update stock request for GRN item " + e.getMessage(), GRNExceptionStatus.GRN_BAD_REQUEST);
        }
    }

	private StockInwardRequest prepareStockInwardRequest(GrnItemEntity grnItem, String location, 
			Map<String, BarcodePriceResponse> barcodePriceResponse, String currency) throws Exception {
        log.info("[prepareStockInwardRequest] barcode {}, facility {}, pid {}, legalOwner {}, updatedBy {}, recAT {}",
                grnItem.getBarcode(), grnItem.getFacility(), Integer.valueOf(grnItem.getPid()),
                grnItem.getLegalOwner(), grnItem.getUpdatedBy(), new Date());
        StockInwardRequest inwardRequestItem = new StockInwardRequest();
		inwardRequestItem.setBarcode(grnItem.getBarcode());
		inwardRequestItem.setFacility(grnItem.getFacility());
		inwardRequestItem.setPid(Integer.valueOf(grnItem.getPid()));
		inwardRequestItem.setUpdatedBy(grnItem.getCreatedBy());
		inwardRequestItem.setPoNumber(grnItem.getPoId());
		inwardRequestItem.setExpiry(grnItem.getExpiryDate());
		inwardRequestItem.setInvoiceNumber(grnItem.getInvoiceRefNum());
		inwardRequestItem.setReferenceNumber(grnItem.getGrnCode());
		inwardRequestItem.setVendor(grnItem.getVendorCode());
		inwardRequestItem.setPutawayNumber(grnItem.getPutawayCode() != null ? 
				String.valueOf(grnItem.getPutawayCode()) : null);
		inwardRequestItem.setReceivedDate(new Date());
		inwardRequestItem.setSourceOperations(SourceOperations.GRN);
		inwardRequestItem.setLocation(location);
		inwardRequestItem.setLegalOwner(grnItem.getLegalOwner());
		inwardRequestItem.setBoxBarcode(grnItem.getQcPassBoxBarcode());
        inwardRequestItem.setReceivedDate(Calendar.getInstance().getTime());
        inwardRequestItem.setActionId(String.valueOf(grnItem.getId()));
        if (CollectionUtils.isEmpty(barcodePriceResponse)) {
        	updateLandedCostDetails(grnItem.getPoId(), grnItem.getPid(), grnItem.getLegalOwner(), 
				inwardRequestItem);
        } else {
        	updateBarcodePriceAndCurrency(grnItem, barcodePriceResponse, currency, inwardRequestItem);
        }
        if (!StringUtils.isEmpty(grnItem.getLotNo())){
            inwardRequestItem.setLotNo(grnItem.getLotNo());
        }
        log.info("[prepareStockInwardRequest] barcode {}, inwardRequestItem {}",
                grnItem.getBarcode(), inwardRequestItem);
		return inwardRequestItem;
	}

	private void updateBarcodePriceAndCurrency(GrnItemEntity grnItem,
			Map<String, BarcodePriceResponse> barcodePriceResponse, String currency,
			StockInwardRequest inwardRequestItem) {
		BarcodePriceResponse barcodePrice = barcodePriceResponse.get(grnItem.getBarcode());
		inwardRequestItem.setLandedCostWithoutTax(new BigDecimal(barcodePrice.getPrice()).setScale(4, RoundingMode.HALF_UP));
		if (StringUtils.isBlank(barcodePrice.getCurrencyCode())) {
			inwardRequestItem.setLandedCostCurrency(currency);
		} else {
			inwardRequestItem.setLandedCostCurrency(barcodePrice.getCurrencyCode());
		}
	}

	@Override
	@Async
	public void updateLandedCostDetails(String poId, String pId) {
		try {
			PurchaseOrder purchaseOrder = purchaseOrderEntityService.getPurchaseOrder(poId);
			updateLandedCostDetails(poId, pId, purchaseOrder.getLegalOwner(), null);
		} catch (Exception ex) {
			log.error("[updateLandedCostDetails] Price update failed for po_id {}, pid {}, legalOwner {}",
					poId, pId, ex);
		}
	}
	
	private void updateLandedCostDetails(String poId, String pId, String legalOwner, 
			StockInwardRequest inwardRequestItem) throws Exception {
		log.info("[updateLandedCostDetails] calculating unit price for po id: {}, pid: {}", poId, pId);
		String landedCostRedisKey = "PURCHASE_ORDER_" + poId + "_PID_" + pId + "_LANDED_COST";
        String landedCostValue = null;
        if (RedisHandler.hasKey(landedCostRedisKey)) {
        	landedCostValue = (String) RedisHandler.redisOps(RedisOps.GET, landedCostRedisKey);
		} else {
			PurchaseOrderItem purchaseOrderItem = purchaseOrderItemEntityService.getPurchaseOrderItemByPoNumAndPid(poId,
					Integer.parseInt(pId));
			PurchaseOrder purchaseOrder = purchaseOrderEntityService.getPurchaseOrder(poId);
			LegalOwner legalOwnerDetails = legalOwnerConfigRepository.findByLegalOwner(legalOwner);
			double landedCostWithOutTax = (purchaseOrderItem.getFinalPrice() - purchaseOrderItem.getCgstRate() - purchaseOrderItem.getIgstRate()
							- purchaseOrderItem.getSgstRate() - purchaseOrderItem.getUgstRate() - purchaseOrderItem.getFlatRate())
							/ purchaseOrderItem.getQuantity() * purchaseOrder.getCurrencyCovRate();
            log.info("[updateLandedCostDetails] calculating unit price for po_id {}, pid {} : currency {}, landedCost {}",
                    poId, pId, landedCostRedisKey, legalOwnerDetails.getOperatingCurrency(), landedCostWithOutTax);
			landedCostValue = legalOwnerDetails.getOperatingCurrency() + "_" + landedCostWithOutTax;
			RedisHandler.redisOps(RedisOps.SETVALUETTL, landedCostRedisKey, landedCostValue, 24L, TimeUnit.HOURS);
		}
        if (inwardRequestItem != null) {
	        inwardRequestItem.setLandedCostWithoutTax(new BigDecimal(landedCostValue.split("_")[1]).setScale(4, RoundingMode.HALF_UP));
	        inwardRequestItem.setLandedCostCurrency(landedCostValue.split("_")[0]);
        }
        log.info("[updateLandedCostDetails] Price successfully saved {}, pid {}, legalOwner {}, landedCostValue {}", 
        		poId, pId, legalOwner, landedCostValue);
    }

    @Override
    public UpdateStockInwardRequest frameUpdateStockRequestForIqcGRNItemV2(List<GrnItemEntity> grnItemsList, ActionStatus operation, String location, String imsOperation) throws Exception {
        log.info("[frameUpdateStockRequestForIqcGRNItemV2] Frame update stock request Iqc for GRN item list: {}, operation {}", grnItemsList, operation.getName());
        UpdateStockInwardRequest updateStockInwardRequest = new UpdateStockInwardRequest();
        List<StockInwardRequest> stockInwardRequestItems = new ArrayList<>();
        for (GrnItemEntity grnItem : grnItemsList) {
        	StockInwardRequest inwardRequestItem = prepareStockInwardRequest(grnItem, location, null, null);
        	stockInwardRequestItems.add(inwardRequestItem);
        	updateStockInwardRequest.setStockInwardRequestList(stockInwardRequestItems);
        }
        updateStockInwardRequest.setOperation(imsOperation);
        log.info("[frameUpdateStockRequestForIqcGRNItemV2] Frame update stock request Iqc is updateStockInwardRequest {}", updateStockInwardRequest);
        return updateStockInwardRequest;
    }

    @Override
    @Async
    public UpdateStocksResponseV2 performStockInward(UpdateStockInwardRequest updateStockInwardRequest) {
        try {
            UpdateStocksResponseV2 updateStocksResponseV2 = null;
            BaseResponseModel baseResponseModel = null;
            String url = imsBaseURL + stockInwardUpdateUrl;
            log.info("[performStockInward] Stock Inward request to ims for request: {} with URL: {}", updateStockInwardRequest, url);
            String response = RetryUtils.postData(url + "?size=" + responseSize, updateStockInwardRequest, String.class);
            objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL).configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            baseResponseModel = objectMapper.readValue(response, BaseResponseModel.class);
            log.info("[performStockInward] baseResponseModel response: {}", baseResponseModel.getData());
            if (baseResponseModel.getData() != null) {
                updateStocksResponseV2 = objectMapper.convertValue(
                        baseResponseModel.getData(), new TypeReference<UpdateStocksResponseV2>() {
                        });
            } else {
                log.error("[performStockInward] Send request to ims for stock inward update failed: {}, response is null", updateStockInwardRequest);
                throw new Exception("[stockInwardUpdate] IMS stock inward update failed for request " +updateStockInwardRequest+ " - Response is null");
            }
            log.info("[performStockInward] UpdateStocksResponseV2 : {}", updateStocksResponseV2);
            return updateStocksResponseV2;
        } catch (Exception e){
            log.error("[performStockInward] Error: Send request to ims for stock inward update failed: {}, {}", updateStockInwardRequest, e.getMessage());
            throw new ApplicationException("Error: Send request to ims for stock inward update failed: " + e.getMessage(), GRNExceptionStatus.GRN_BAD_REQUEST);
        }
    }

    @Override
    public UpdateStockInwardRequest createUpdateStockInwardRequest(List<GrnItemEntity> grnItems, String imsOperation,
                                                                   Map<String, BarcodePriceResponse> barcodePriceResponse, String currency, Map<String, String> barcodeUrlMap) throws CustomException {
    	UpdateStockInwardRequest updateStockInwardRequest = new UpdateStockInwardRequest();
    	List<StockInwardRequest> stockInwardRequestItems = new ArrayList<>();
    	updateStockInwardRequest.setStockInwardRequestList(stockInwardRequestItems);
    	for (GrnItemEntity grnItem : grnItems) {
    		try {
    			log.info("[frameUpdateStockRequestForGRNItemV2} GRN item barcode: {}, operation {}",
    					grnItem.getBarcode(), imsOperation);
    			String location = getIMSLocation(grnItem);
				StockInwardRequest inwardRequestItem = prepareStockInwardRequest(grnItem, location, barcodePriceResponse, currency);
                if(barcodeUrlMap.containsKey(grnItem.getBarcode())) {
                    inwardRequestItem.setBarcodeUrl(barcodeUrlMap.get(grnItem.getBarcode()));
                }
    			log.info("[frameUpdateStockRequestForGRNItemV2] barcode: {}, operation {}, inwardRequestItem {}",
    					grnItem.getBarcode(), imsOperation, inwardRequestItem);
    			stockInwardRequestItems.add(inwardRequestItem);
    			updateStockInwardRequest.setOperation(imsOperation);
    			updateStockInwardRequest.setUpdatedBy(grnItem.getUpdatedBy());
    			log.info("[frameUpdateStockRequestForGRNItemV2] GRN item barcode: {}, updateStockInwardRequest {}",
    					grnItem.getBarcode(), updateStockInwardRequest);
    		} catch (Exception e) {
    			log.error("[frameUpdateStockRequestForGRNItemV2] Error: Frame update stock request for GRN item {}, operation {}, error {}", 
    					grnItem, imsOperation, e.getMessage());
    			throw new ApplicationException("Error: Frame update stock request for GRN item " + e.getMessage(), GRNExceptionStatus.GRN_BAD_REQUEST);
    		}
		}
    	return updateStockInwardRequest;
    }

	private String getIMSLocation(GrnItemEntity grnItem) {
		String location = grnItem.getFacility() + "."
				+ (StringUtils.equalsIgnoreCase(grnItem.getQcStatus(), "fail") ? "BAD" : "GOOD");
		return location;
	}

    public static RestTemplate getRestTemplate(int timeout){
        RestTemplate restTemplate=new RestTemplate();
        SimpleClientHttpRequestFactory rf = new SimpleClientHttpRequestFactory();
        rf.setReadTimeout(timeout);
        rf.setConnectTimeout(timeout);
        restTemplate.setRequestFactory(rf);
        return restTemplate;
    }
}