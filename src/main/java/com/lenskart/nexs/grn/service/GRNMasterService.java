package com.lenskart.nexs.grn.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.lenskart.nexs.baseResponse.BaseResponseModel;
import com.lenskart.nexs.common.entity.entityService.PurchaseOrderEntityService;
import com.lenskart.nexs.common.entity.entityService.PurchaseOrderItemEntityService;
import com.lenskart.nexs.common.entity.entityService.grn.*;
import com.lenskart.nexs.common.entity.entityService.invoice.PurchaseInvoiceEntityService;
import com.lenskart.nexs.common.entity.entityService.invoice.PurchaseInvoiceItemEntityService;
import com.lenskart.nexs.common.entity.entityServiceImpl.VendorMasterEntityServiceImpl;
import com.lenskart.nexs.common.entity.po.PurchaseOrderItem;
import com.lenskart.nexs.common.entity.po.grn.*;
import com.lenskart.nexs.common.entity.po.invoice.PurchaseInvoiceEntity;
import com.lenskart.nexs.common.entity.po.invoice.PurchaseInvoiceItemEntity;
import com.lenskart.nexs.common.entity.repositories.d365.GrnD365Repository;
import com.lenskart.nexs.common.entity.repositories.d365.PoD365Repository;
import com.lenskart.nexs.common.entity.repositories.grn.GrnNumGenRepository;
import com.lenskart.nexs.common.entity.vim.VendorMaster;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.constants.RedisOps;
import com.lenskart.nexs.ems.enums.ExceptionType;
import com.lenskart.nexs.ems.enums.Source;
import com.lenskart.nexs.ems.model.AutoGrnScanDetails;
import com.lenskart.nexs.ems.model.EmsExceptionEvent;
import com.lenskart.nexs.exception.CustomException;
import com.lenskart.nexs.fms.model.entity.LegalOwner;
import com.lenskart.nexs.fms.model.repo.FacilityDetailsRepository;
import com.lenskart.nexs.fms.model.repo.LegalOwnerConfigRepository;
import com.lenskart.nexs.fms.model.response.FacilityDetailsResponse;
import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.connector.FinanceConsumerConnector;
import com.lenskart.nexs.grn.connector.WMSConnector;
import com.lenskart.nexs.grn.constants.CSVHeaders.GRNDetailsCSV;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.constants.GRNQcLogEvents;
import com.lenskart.nexs.grn.constants.QualifierConstants;
import com.lenskart.nexs.grn.dao.*;
import com.lenskart.nexs.grn.db.Queries;
import com.lenskart.nexs.grn.dto.GRNMasterMetaDTO;
import com.lenskart.nexs.grn.dto.request.*;
import com.lenskart.nexs.grn.dto.response.*;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.mapper.GRNMasterMapper;
import com.lenskart.nexs.grn.mapper.GRNMasterMetaDTOMapper;
import com.lenskart.nexs.grn.model.*;
import com.lenskart.nexs.grn.putaway.service.PutawayService;
import com.lenskart.nexs.grn.service.impl.FinancePlatformService;
import com.lenskart.nexs.grn.util.*;
import com.lenskart.nexs.ims.request.UpdateStockInwardRequest;
import com.lenskart.nexs.ims.response.ItemStockUpdateResponseV2;
import com.lenskart.nexs.ims.response.UpdateStocksResponseV2;
import com.lenskart.nexs.po.common.serviceutil.config.LegalOwnerBatchConfig;
import com.lenskart.nexs.po.common.serviceutil.connector.VendorMgmtConnector;
import com.lenskart.nexs.putaway.model.response.CreatePutawayItemResponse;
import com.lenskart.nexs.putaway.model.response.CreatePutawayResponse;
import com.lenskart.nexs.service.PdfGeneratorService;
import com.lenskart.nexs.service.RedisHandler;
import com.nexs.po.common.enums.GrnImsSyncStatusEnum;
import com.nexs.po.common.enums.InvoiceLevelEnum;
import com.nexs.po.common.enums.IqcGrnProductStatusEnum;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.core.SchedulerLock;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.server.ResponseStatusException;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.SocketTimeoutException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.Year;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class GRNMasterService implements GRNConstants {

	@Autowired
	@Qualifier(QualifierConstants.JPA_GRN_MASTER_DAO)
	private GRNMasterDAO grnMasterDAO;

	@Autowired
	private GRNConfig grnConfig;

	@Autowired
	@Qualifier(QualifierConstants.JPA_QC_STATUS_DAO)
	private QcStatusDAO qcStatusDAO;

	@Autowired
	private CacheDAO cacheDAO;

	@Autowired
	@Qualifier(QualifierConstants.JPA_USER_ACTIVITY_DAO)
	private UserActivityDAO userActivityDAO;

	@Autowired
	@Qualifier(QualifierConstants.JPA_GRN_PID_DAO)
	private GRNPIDDAO grnPidDao;

	@Autowired
	GRNQcLogService grnQcLogService;

	@Autowired
	@Qualifier(QualifierConstants.JPA_GRN_ITEM_DAO)
	private GRNItemDAO grnItemDAO;

	@Autowired
	private StringRedisTemplate stringRedisTemplate;

	@Autowired
	private GRNItemService grnItemService;

	@Autowired
	private GRNPIDMasterService grnPidMasterService;

//	@Autowired
//	private QcSamplingImpl qcSampling;

	@Autowired
	private GRNCacheService grnCacheService;

	@Autowired
	private PdfGeneratorService pdfGeneratorService;

	@Autowired
	private FacilityDetailsUtils facilityDetailsUtils;

	@Autowired
	private UserDetailsService userDetailsService;

	@Autowired
	private PutawayService putawayService;

	@Autowired
	private PoInvoiceService poInvoiceService;

	@Autowired
	PurchaseOrderEntityService purchaseOrderEntityService;

	@Autowired
	PoD365Repository poD365Repository;

	@Autowired
	GrnD365Repository grnD365Repository;

	@Autowired
	PurchaseInvoiceEntityService purchaseInvoiceEntityService;

	@Autowired
	GRNCacheServiceUtils grnCacheServiceUtils;

	@Autowired
	PurchaseOrderItemEntityService purchaseOrderItemEntityService;

	@Autowired
	private PurchaseInvoiceItemEntityService purchaseInvoiceItemEntityService;

	@Autowired
	IqcGrnProductEntityService iqcGrnProductEntityService;

	@Autowired
	GrnMasterEntityService grnMasterEntityService;

	@Autowired
	GrnNumGenEntityService grnNumGenEntityService;

	@Autowired
	GrnNumGenRepository grnNumGenRepository;

	@Autowired
	GrnD365SyncEntityService grnD365SyncEntityService;

	@Autowired
	GrnItemEntityService grnItemEntityService;

	@Autowired
	@Qualifier("closeGrnCreateGrnItemPutawayHandler")
	private CloseGrnCreateGrnItemPutawayHandler closeGrnCreateGrnItemPutawayHandler;

	@Value("${close.grn.reconcile.exclude.category:CL,PL}")
	private Set<String> closeGrnReconcileExcludeCategory;

	@Value("${kafka.topic.ems.exception.handler}")
	private String emsKafkaTopic;

	@Autowired
	private LegalOwnerBatchConfig legalOwnerBatchConfig;

	@Value("${grn.sequence.prefix:G}")
	private String grnSequencePrefix;

	@Value("${grn.default.sequence.length:18}")
	private int grnDefaultSeqLength;

	@Value("${grn.default.sequence.max.length:20}")
	private int grnDefaultSeqMaxLength;

	@Value("${grn.skip.slow.invoice.id.query:true}")
	private boolean skipSlowInvoiceIdQuery;

	@Autowired
	private IMSService imsService;

	@Autowired
	private FinanceConsumerConnector financeConsumerConnector;

	@Autowired
	private WMSConnector wmsConnector;
	@Autowired
	private FinancePlatformService financePlatformService;

	@Value("${finance.consumer.url}")
	private String financeConsumerUrl;

	@Value("${finance.consumer.enable.flag:true}")
	private boolean enableFinanceConsumerFlow;
	@Value(value = "${enable.sync.to.finance.platform:true}")
	private boolean enableSyncToPlatform;

	private final static String grnEventCreateUrl = "/inward/api/v1/grn/create";

	@Value("#{'${box.barcode.disable.legal.owner.list}'.split(',')}")
	private Set<String> boxBarcodeDisableLegalOwnerList;

	private static final ObjectMapper objectMapper = new ObjectMapper();

	@Value("${show.facility.old.date:1750766400}") // Default epoch for Tuesday, 24 June 2025 12:00:00
	private long showOldFacilityEpochDate;

	@Value("${facility.old.name:Lenskart Solutions Private Limited}") // Default old name
	private String oldFacilityName;

	@Value("${facility.old.name:Lenskart Solutions Limited (formerly known as Lenskart Solutions Private Limited}") // Default old name
	private String newFacilityName;

	@Value("#{'${new.old.validate.facility.code:NXS1,NXS2,EXE2,INSR}'.split(',')}")
	private Set<String> newOrOldValidateFacilityCodeCheck;

	@Autowired
	private VendorMasterEntityServiceImpl vendorMasterEntityService;

	@Autowired
	LegalOwnerConfigRepository legalOwnerConfigRepository;

	@Autowired
	private FacilityDetailsRepository facilityDetailsRepository;

	@Autowired
	VendorMgmtConnector vendorMgmtConnector;

	@Logging
	public CreateGRNResponseDTO createGRN(CreateGRNMasterDTO createGRNMasterDTO, String facilityCode, String grnType) throws Exception {
		log.info("[createGRN] createGRNMasterDTO {}, facilityCode {}, grnType {}",
				createGRNMasterDTO, facilityCode, grnType);
		try {
			validateGRN(createGRNMasterDTO.getInvoice(), facilityCode);
			GRNMaster grnMaster = GRNMasterMapper.INSTANCE.mapToGRNMaster(createGRNMasterDTO);
//			grnMaster.setBatchNo(createGRNMasterDTO.getInvoice().getBatchNo());
			grnMaster = GRNUtils.convertDate(grnMaster);

//			validate batch number for inwards
//			log.info("[createGRN] Validate BatchNum createGRNMasterDTO {}, facilityCode {}, grnType {}",
//					createGRNMasterDTO, facilityCode, grnType);
//			validateBatchNumberFromInvoice(createGRNMasterDTO, grnType);

			if (facilityDetailsUtils.isUnicomFacility(createGRNMasterDTO.getFacilityCode()) && grnConfig.isUnicomCallEnabled()) {
				String unicomGrnCode = getUnicomGrnCode(grnMaster);
				grnMaster.setUnicomGrnCode(unicomGrnCode);
			} else {
				log.info("GRN doesn't belong to unicom facility, or unicom calls are blocked. Not setting unicom grn code in grnMaster object : {} ", grnMaster.getGrnCode());
			}

			addGrnSequenceInDB(facilityCode, grnMaster);
			GRNMasterMetaDTO grnMasterMetaDTO = GRNMasterMetaDTOMapper.INSTANCE.mapToGRNMasterMetaDTO(grnMaster);
			if (createGRNMasterDTO.getType() != null && createGRNMasterDTO.getType().equalsIgnoreCase("CL")) {
				grnMaster.setGrnType(GRNConstants.CL_TYPE);
			} else if (StringUtils.isNotBlank(grnType)) {
				grnMaster.setGrnType(grnType);
			} else {
				grnMaster.setGrnType(fetchGrnType(createGRNMasterDTO));
			}
			log.info("[createGRN] invoiceNum {}, grnType {}", createGRNMasterDTO.getInvoice().getInvoiceId(), grnMaster.getGrnType());

			Double currencyConversion = getCurrencyConversionRate(createGRNMasterDTO.getInvoice());
			log.info("[createGRN] invoiceNum {}, grnType {}, currencyConversion {}",
					createGRNMasterDTO.getInvoice().getInvoiceId(), grnMaster.getGrnType(), currencyConversion);
			grnMaster.setCurrencyCovRate(currencyConversion);

			boolean grnCreated = grnMasterDAO.createGRN(grnMaster);
			if (!grnCreated)
				throw new ApplicationException("Create GRN was unsuccessful", GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			return GRNUtils.getCreateResponseDTO(grnMasterMetaDTO, grnMaster, GRN_STATUS_CREATED);
		} catch (Exception e){
			log.error("[createGRN] Unable to create GRN createGRNMasterDTO {}, error {}",
					createGRNMasterDTO, e.getMessage());
			throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Unable to create grn : " + e.getMessage());
		}
	}

	private Double getCurrencyConversionRate(Invoice invoice) throws Exception {
		try {
			log.info("[getCurrencyConversionRate] Invoice: Vendor {}, LegalOwner {}", invoice.getVendor(), invoice.getLegalOwner());
			VendorMaster vendorMaster = vendorMasterEntityService.findByUnicommVendorCodeAndEnabledAndLegalOwner(invoice.getVendor() ,true, invoice.getLegalOwner());
			if (vendorMaster.getCurrency() == null) {
				throw new CustomException("Vendor does not exist for " + invoice.getVendor() + ", legalOwner " + invoice.getLegalOwner(), 405);
			}

			LegalOwner legalOwnerConfig = legalOwnerConfigRepository.findByLegalOwner(invoice.getLegalOwner());
			log.info("[getCurrencyConversionRate] vendorMaster {} ,legalOwnerConfig {}", vendorMaster, legalOwnerConfig);
			if (legalOwnerConfig == null || legalOwnerConfig.getOperatingCurrency() == null || legalOwnerConfig.getOperatingCurrency().isEmpty()) {
				log.info("[getCurrencyConversionRate] LegalOwnerConfig is null or the specified currency is invalid {}", legalOwnerConfig);
				throw new Exception("LegalOwnerConfig is null or the specified currency is invalid");
			}

			String fromCurrency = vendorMaster.getCurrency();
			String toCurrency = legalOwnerConfig.getOperatingCurrency();
			log.info("[getCurrencyConversionRate] vendorMaster {} ,legalOwnerConfig {}", vendorMaster, legalOwnerConfig);
			return vendorMgmtConnector.getCurrencyConversionRate(fromCurrency, toCurrency);
		} catch (Exception e) {
			log.error("[setVendorDetail] Unable to fetch currency conversion rate, Vendor {}, LegalOwner {}, error {}",
					invoice.getVendor(), invoice.getLegalOwner(), e.getMessage());
			throw new CustomException(e.getMessage(), 405);
		}
	}

	private String fetchGrnType(CreateGRNMasterDTO createGRNMasterDTO) throws JsonProcessingException {
		Map<String, List<String>> fetchVendorAndLegalOwnerMapping = objectMapper.readValue(grnConfig.getGrnDOCriteriaMapping(), new TypeReference<Map<String, List<String>>>() {
		});
		log.info("[fetchGrnType] invoiceNum {}, fetchVendorAndLegalOwnerMapping {}", createGRNMasterDTO.getInvoice().getInvoiceId(), fetchVendorAndLegalOwnerMapping);
		String vendorCode = createGRNMasterDTO.getInvoice().getVendor();
		String legalOwner = createGRNMasterDTO.getInvoice().getLegalOwner();
		log.info("[fetchGrnType] invoiceNum {}, vendorCode {}, legalOwner {}", createGRNMasterDTO.getInvoice().getInvoiceId(), vendorCode, legalOwner);
		if (fetchVendorAndLegalOwnerMapping.containsKey(legalOwner) && fetchVendorAndLegalOwnerMapping.get(legalOwner).contains(vendorCode)) {
			return GRNConstants.DO;
		}
		return GRNConstants.REGULAR_TYPE;
	}

//	private void validateBatchNumberFromInvoice(CreateGRNMasterDTO createGRNMasterDTO, String grnType) {
//		log.info("[validateBatchNumberFromInvoice] createGRNMasterDTO - {}, grnType - {} ", createGRNMasterDTO, grnType);
//		Integer categoryId = createGRNMasterDTO.getPo().getPids().get(0).getCategoryId();
//		log.info("[validateBatchNumberFromInvoice] categoryId of first record is {} ", categoryId);
//		if (!"JIT".equalsIgnoreCase(grnType) && legalOwnerBatchConfig.getMapping().containsKey(createGRNMasterDTO.getInvoice().getLegalOwner()) && legalOwnerBatchConfig.getMapping().get(createGRNMasterDTO.getInvoice().getLegalOwner()).contains(categoryId)) {
////			if (createGRNMasterDTO.getInvoice().getBatchNo() == null || createGRNMasterDTO.getInvoice().getBatchNo().isEmpty()) {
////				log.error("[validateBatchNumberFromInvoice] Batch num is mandatory for invoice ref num {} ",
////						createGRNMasterDTO.getInvoice().getInvoiceRefNum());
////				throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Batch number is mandatory.");
////			}
//
//			PurchaseInvoiceEntity invoiceEntity = purchaseInvoiceEntityService.getInvoiceByReferenceNumber(
//					Integer.valueOf(createGRNMasterDTO.getInvoice().getInvoiceRefNum()), true);
//			if (invoiceEntity.getBatchNoList() == null) {
//				log.error("[validateBatchNumberFromInvoice] Batch num is empty for invoice ref num {} ",
//						createGRNMasterDTO.getInvoice().getInvoiceRefNum());
//				throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Batch number is empty for invoice.");
//			} else {
//				log.info("[validateBatchNumberFromInvoice] invoice num {}, invoice batch num list {}",
//						createGRNMasterDTO.getInvoice().getInvoiceRefNum(), invoiceEntity.getBatchNoList());
//				Set<String> batchNumList = Arrays.stream(invoiceEntity.getBatchNoList().replace("[","").
//						replace("]","").split(",")).map(String::trim).collect(Collectors.toSet());
//				log.info("[validateBatchNumberFromInvoice] invoice num {}, batchNumList{ {}",
//						createGRNMasterDTO.getInvoice().getInvoiceRefNum(), batchNumList);
////				if (!batchNumList.contains(createGRNMasterDTO.getInvoice().getBatchNo().trim())) {
//					log.error("[validateBatchNumberFromInvoice] Batch num {} does not exist in invoice ref num {}" +
////									" batch list {}", createGRNMasterDTO.getInvoice().getBatchNo(),
////							createGRNMasterDTO.getInvoice().getInvoiceRefNum(), batchNumList);
////					throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR,
////							"Batch num " + createGRNMasterDTO.getInvoice().getBatchNo() +
////									" does not exist in invoice batch list " + batchNumList);
////				}
//			}
//		}
//	}

    public void addGrnSequenceInDB(String facilityCode, GRNMaster grnMaster) throws Exception {
		GrnNumGenEntity grnNumGenEntityCurrent = grnNumGenEntityService.findTopByFacilityAndCreatedAtOrderByDescending(facilityCode);
		log.info("grnNumGenEntityCurrent before increment", grnNumGenEntityCurrent);
		if (grnNumGenEntityCurrent == null) {
			grnNumGenEntityCurrent = createGrnSequence(facilityCode, grnMaster.getCreatedBy());
		}
		grnNumGenEntityCurrent = GRNUtils.getGRNSequence(grnConfig.getEnvPrefix(), grnConfig.getKeyPrefix(), grnNumGenEntityCurrent);
		log.info("grnNumGenEntityCurrent after increment",grnNumGenEntityCurrent);
		grnMaster.setGrnCode(grnNumGenEntityCurrent.getGrnNumGen());
		grnNumGenRepository.save(grnNumGenEntityCurrent);
		log.info("saved in grn_sequence successfully");
	}

	private GrnNumGenEntity createGrnSequence(String facilityCode, String createdBy) {
		log.info("[createGrnSequence] facilityCode {}, createdBy {}", facilityCode, createdBy);
		GrnNumGenEntity grnNumGenEntity = new GrnNumGenEntity();
		grnNumGenEntity.setGrnNumGen(generateGrnNumSequence(facilityCode));
		grnNumGenEntity.setFacilityCode(facilityCode);
		grnNumGenEntity.setEnabled(true);
		grnNumGenEntity.setCreatedBy(createdBy);
		grnNumGenEntity.setUpdatedBy(createdBy);
		return grnNumGenRepository.save(grnNumGenEntity);
	}

	private String generateGrnNumSequence(String facilityCode) {
		log.info("[generateGrnNum] facilityCode {}", facilityCode);
		String currentYear = String.valueOf(Year.now().getValue()).substring(2);
		String grnNum = grnSequencePrefix + facilityCode + "-" + currentYear + "-";
		int grnPostFixLength = grnDefaultSeqLength - grnNum.length();
		if (grnPostFixLength < 4) {
			grnPostFixLength = grnDefaultSeqMaxLength - grnNum.length();
		}
		for (int i = 0; i < grnPostFixLength; i++) {
			grnNum = grnNum + "0";
		}
		log.info("[generateGrnNum] facilityCode {}, grnNum {}", facilityCode, grnNum);
		return grnNum;
	}

	@Logging
	public GetGRNResponseDTO getGRN(String grnCode) throws Exception {
		GRNMaster grnMaster = grnMasterDAO.getGRNMaster(grnCode, MDC.get("USER_ID"));

		log.info("[validateJitPOType] Get Grn Details po {}, type {}", grnMaster.getPoId(), grnMaster.getGrnType());
		validatePOType(grnMaster.getPoId(), grnMaster.getInvoiceId(), grnMaster.getGrnType());

		if (grnMaster == null)
			throw new ResponseStatusException(HttpStatus.NOT_FOUND, "No record found for grnCode : " + grnCode);

		if(MDC.get("FACILITY_CODE") != null && grnMaster.getFacility() != null && !grnMaster.getFacility().equals(MDC.get("FACILITY_CODE"))) {
			throw new Exception("Incorrect facility chosen in header");
		}

		GRNMasterMetaDTO grnMasterMetaDTO = null;

		List<GRNPidStatusDTO> pids = new LinkedList<>();
		grnMasterMetaDTO = GRNMasterMetaDTOMapper.INSTANCE.mapToGRNMasterMetaDTO(grnMaster);
		//if (!grnMaster.getEstimatedTotalQuantity().isEmpty()) {


			List<GRNPIDMaster> grnPids = grnPidDao.getGRNPIDDetails(Arrays.asList(grnMaster.getGrnCode()));
			for (GRNPIDMaster pid : grnPids) {
				pids.add(new GRNPidStatusDTO(pid.getPid(), pid.getPidStatus()));
			}
		//}

		userDetailsService.addUserDetails(MDC.get("USER_ID"));
		return GRNUtils.getGRNResponseDTO(grnMasterMetaDTO, grnMaster, pids, grnMaster.getGrnStatus());
	}

	@Logging
	public void editGRN(GRNUpdateDTO grnUpdateDTO, String facilityCode) throws Exception {
		grnUpdateDTO.setUpdatedBy(MDC.get("USER_ID"));
		grnUpdateDTO.setFacilityCode(facilityCode);
		GRNUtils.convertDate(grnUpdateDTO);
		int updated = grnMasterDAO.editGRN(grnUpdateDTO);
		if(updated <= 0)
			throw new ApplicationException("Update was unsuccessful", GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
	}

	public void closeGRN(String grnCode, String facilityCode, GRNMaster grnMaster, boolean createPutaway) throws Exception {
		closeGRN(grnCode, facilityCode, grnMaster, createPutaway, true);
	}

	@Logging
	public void closeGRN(String grnCode, String facilityCode, GRNMaster grnMaster, boolean createPutaway, boolean imsValidationCheck) 
			throws Exception {
		closeGRN(grnCode, facilityCode, grnMaster, null, createPutaway, imsValidationCheck, false);
	}

	@Logging
	public void closeGRN(String grnCode, String facilityCode, GRNMaster grnMaster, String operation, boolean createPutaway, boolean imsValidationCheck, 
			boolean emsCall) throws Exception {
		log.info("[closeGRN] Close GRN: {}", grnCode);
		try {
			if (grnMaster != null && GRNConstants.GRN_STATUS_CLOSED.equalsIgnoreCase(grnMaster.getGrnStatus())) {
				if (emsCall) {
					return;
				}
				throw new Exception("GRN is already closed");
			}
			if (StringUtils.isBlank(operation)) {
				operation = GRNConstants.GRN_QC_PASS;
			}
			log.info("[closeGRN] Close GRN: {}, type {}", grnCode, grnMaster.getGrnType());
			if (imsValidationCheck && !GRNConstants.JIT_TYPE.equalsIgnoreCase(grnMaster.getGrnType())) {
				validateIMSSyncStatusForAllGrnItems(grnMaster, operation);
			}
			if (createPutaway && !GRNConstants.JIT_TYPE.equalsIgnoreCase(grnMaster.getGrnType()) &&
					grnConfig.getCloseGrnSyncUnsynedBarcodePutawayCount() > 0) {
				validatePutawayCreatedForAllGrnItems(grnMaster, grnConfig.getCloseGrnSyncUnsynedBarcodePutawayCount());
			}
			
			checkEligibilityForIQCFromInvoice(grnCode, grnMaster.getInvoiceId());
			validateGRNItemCountWithPoAndInvoice(grnCode, grnMaster.getGrnType(), grnMaster.getPoId());

			if (createPutaway) {
				markPutawayPending(grnCode, grnMaster.getGrnType());
			}
			if(GRNConstants.DO.equals(grnMaster.getGrnType())){
				log.info("[closeGRN] DO GRN close for grnCode {}", grnCode);
				markBarcodeAsNullInWMS(grnCode);
				log.info("[closeGRN] Nullified Barcodes in wms for grnCode {}", grnCode);

			}

			log.info("[closeGRN] GRN close called in daemon thread for grnCode {}", grnCode);
			List<GRNPIDMaster> grnpidMasterList = grnPidDao.getGRNPIDS(grnCode);
			checkPidStatuses(grnpidMasterList);
			log.info("[closeGRN] closeGrnReconcileExcludeCategory {}, grn type {}",
					closeGrnReconcileExcludeCategory, grnMaster.getGrnType());
			if (closeGrnReconcileExcludeCategory!= null &&
					(closeGrnReconcileExcludeCategory.contains(grnMaster.getGrnType()) ||
							closeGrnReconcileExcludeCategory.contains("ALL"))) {
				log.info("[closeGRN] Not Reconciling data for grnCode {}", grnCode);
				syncGrnDataToInvoice(grnCode, facilityCode, grnpidMasterList, grnMaster.getGrnType());
				pushCloseGrnCallInKafka(grnCode, grnMaster.getFacility(), false);
			} else {
				log.info("[closeGRN] Reconciling data for grnCode {}", grnCode);
				changeGrnStatusToClosed(grnCode, facilityCode, grnMaster.getGrnType());
				pushCloseGrnCallInKafka(grnCode, grnMaster.getFacility(), true);
			}
			releaseEmptyBoxes(grnCode, facilityCode);
			if (!grnpidMasterList.isEmpty())
				redisCleanUp(grnCode, grnpidMasterList);
		} catch (Exception e){
			log.error("[closeGRN] Close GRN: {}, {}", grnCode, e.getMessage());
			throw new ApplicationException(e.getMessage(),
					GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	private void markBarcodeAsNullInWMS(String grnCode) throws Exception {
		log.info("[markBarcodeAsNullInWMS] grnCode {}", grnCode);
		List<GrnItemEntity> grnItemEntities = grnItemEntityService.findByGrnCode(grnCode);
		List<String> barcodeList = grnItemEntities.stream().map(GrnItemEntity::getBarcode).collect(Collectors.toList());
		if(!barcodeList.isEmpty()) {
			log.info("[markBarcodeAsNullInWMS] Calling wms for grnCode {}, barcodeList{}", grnCode, barcodeList);
			wmsConnector.markBarcodeNull(barcodeList);
		}
	}

	private void validateIMSSyncStatusForAllGrnItems(GRNMaster grnMaster, String operation) {
		String grnCode = grnMaster.getGrnCode();
		try {
			log.info("[validateIMSSyncStatusForAllGrnItems] grnCode {}, operation {}", grnCode, operation);
			String redisKey = "CLOSE_GRN_IMS_SYNC_RETRY_" + grnCode;
			checkIfRedisKeyExists(redisKey);

			List<GrnItemEntity> missingIMSStatusGrnItemList = grnItemEntityService.findByGrnCodeAndImsSyncStatusIsNull(grnCode);
			log.info("[validateIMSSyncStatusForAllGrnItems] grnCode {}, missingIMSStatusGrnItemList {}",
					grnCode, missingIMSStatusGrnItemList);
			List<String> failedBarcodeList = new ArrayList<>();

			if (!missingIMSStatusGrnItemList.isEmpty()) {
				createGrnItemInIms(grnCode, missingIMSStatusGrnItemList, failedBarcodeList, operation);
				log.info("[validateIMSSyncStatusForAllGrnItems] Success call to IMS service for grn code {}," +
						" failedBarcodeList {}", grnCode, failedBarcodeList);
			}

			List<GrnItemEntity> updateIMSSyncStatusGrnItemList = new ArrayList<>();
			for(GrnItemEntity grnItem: missingIMSStatusGrnItemList){
				log.info("[validateIMSSyncStatusForAllGrnItems] grnCode {}, Update ims status in grn items barcode {}",
						grnCode, grnItem.getBarcode());
				if(!failedBarcodeList.contains(grnItem.getBarcode())){
					grnItem.setImsSyncStatus(GrnImsSyncStatusEnum.GRN_DONE);
					grnItem.setUpdatedBy("SYSTEM");
					grnItem.setUpdatedAt(new Date());
					updateIMSSyncStatusGrnItemList.add(grnItem);
				}
			}

			log.info("[validateIMSSyncStatusForAllGrnItems] grnCode {}, save ims sync status in grn items barcode {}",
					grnCode, updateIMSSyncStatusGrnItemList);
			if (!updateIMSSyncStatusGrnItemList.isEmpty())
				grnItemEntityService.saveOrUpdateAll(updateIMSSyncStatusGrnItemList);

			if (!failedBarcodeList.isEmpty()) {
				log.info("[validateIMSSyncStatusForAllGrnItems] grn code {}, failedBarcodeList {}", grnCode, failedBarcodeList);
				throw new ApplicationException("Unable to sync barcode in IMS: Delete " + failedBarcodeList.toString() + " from GRN", GRNExceptionStatus.GRN_BAD_REQUEST);
			}
		} catch (Exception e) {
			log.error("[validateIMSSyncStatusForAllGrnItems] grnCode {}: error {} ", grnCode, e.getMessage());
			throw new ApplicationException("Unable to create putaway for all grn items: " + e.getMessage(),
					GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	private void createGrnItemInIms(String grnCode, List<GrnItemEntity> missingIMSStatusGrnItemList,
									List<String> failedBarcodeList, String operation) {
		try {
			log.info("[createGrnItemInIms] Create Barcode in IMS for grnCode: {}, missingIMSStatusGrnItemList {}, operation {}",
					grnCode, missingIMSStatusGrnItemList, operation);
			UpdateStocksResponseV2 updateStocksResponseV2 = new UpdateStocksResponseV2();
			for (GrnItemEntity grnItemEntity: missingIMSStatusGrnItemList){
				UpdateStockInwardRequest updateStockInwardRequest = imsService.frameUpdateStockRequestForGRNItemV2(grnItemEntity, operation);
				log.info("[createGrnItemInIms], barcode {}, updateStockInwardRequest {}", grnItemEntity.getBarcode(), updateStockInwardRequest);
				updateStocksResponseV2 = imsService.performStockInward(updateStockInwardRequest);
				log.info("Sent GRN item details in IMS, barcode {}, response {}", grnItemEntity.getBarcode(), updateStocksResponseV2);
			}

			validateImsResponse(updateStocksResponseV2, failedBarcodeList);
			log.info("[createGrnItemInIms] Create Barcode in IMS for grnCode: {}, failedBarcodeList {}"
					, grnCode, failedBarcodeList);
		} catch (Exception e) {
			log.error("[createGrnItemInIms] Create Barcode in IMS for grnCode: {}, error {}"
					, grnCode, e.getMessage());
			throw new ApplicationException(e.getMessage(), GRNExceptionStatus.GRN_BAD_REQUEST);
		}
	}

	private void validateImsResponse(UpdateStocksResponseV2 updateStocksResponseV2, List<String> failedBarcodeList) {
		log.info("[validateImsResponse] failedBarcodeList {}", updateStocksResponseV2);
		if(updateStocksResponseV2 != null && updateStocksResponseV2.getItemStockUpdateResponseV2List() != null
				&& !updateStocksResponseV2.getItemStockUpdateResponseV2List().isEmpty()){
			for(ItemStockUpdateResponseV2 itemStockUpdateResponseV2: updateStocksResponseV2.getItemStockUpdateResponseV2List()){
				if(!itemStockUpdateResponseV2.isSuccess()){
					failedBarcodeList.add(itemStockUpdateResponseV2.getBarcode());
				}
			}
		}
		log.info("[validateImsResponse] failedBarcodeList {}", failedBarcodeList);
	}

	private void checkEligibilityForIQCFromInvoice(String grnCode, String invoiceId) {
		log.info("[checkEligibilityForIQCFromInvoice] grnCode {}, invoiceId {}", grnCode, invoiceId);
		List<PurchaseInvoiceItemEntity> invoiceItemEntities = purchaseInvoiceItemEntityService.
				getByInvoiceRefNumber(Integer.parseInt(invoiceId), true);
		if(invoiceItemEntities != null && invoiceItemEntities.get(0).getIqcSamplingPercent() > 0){
			log.info("[checkEligibilityForIQCFromInvoice] grnCode {}, invoiceId {} IQC sampling is > 0 ",
					grnCode, invoiceId);
			List<IqcGrnProductEntity> iqcGrnProductEntities = iqcGrnProductEntityService.findByGrnCode(grnCode);
			if(iqcGrnProductEntities.size() == 0){
				List<GrnItemEntity> grnItemEntities = grnItemEntityService.findByGrnCode(grnCode);
				if(!grnItemEntities.isEmpty()) {
					log.error("[checkEligibilityForIQCFromInvoice] Unable to close grn, Please complete iqc before closing grnCode {}", grnCode);
					throw new ApplicationException("Unable to close grn, Please complete iqc before closing grnCode " + grnCode,
							GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
				} else{
					log.info("[checkEligibilityForIQCFromInvoice] grnCode {}, invoiceId {} IQC sampling is > 0," +
									" but grn item does not exist",
							grnCode, invoiceId);
				}
			}

			for (IqcGrnProductEntity iqcGrnProductEntity : iqcGrnProductEntities) {
				if (!IqcGrnProductStatusEnum.IQC_COMPLETE.equals(iqcGrnProductEntity.getStatus())) {
					log.error("[checkEligibilityForIQCFromInvoice] Unable to close grn, grn box status should be IQC_COMPLETE before closing it grnCode {}, boxCode {}",
							grnCode, iqcGrnProductEntity.getBoxCode());
					throw new ApplicationException("Please mark grn " + grnCode + " box code " +
							iqcGrnProductEntity.getBoxCode() + " as IQC_COMPLETE before closing",
							GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
				}
			}
		}
	}

	public void closePutawayGRN(String grnCode, String grnType) {
		if (GRNConstants.JIT_TYPE.equalsIgnoreCase(grnType)) {
			log.info("[closeGRN] Not calling putaway for jit type. Grn code {}, type {}", grnCode, grnType);
		} else {
			log.info("[closeGRN] Close GRN in putaway {}, type {}", grnCode, grnType);
			putawayService.closeGRN(grnCode);
			log.info("Close GRN in putaway is successful {}, type {}", grnCode, grnType);
		}
	}

	private void markPutawayPending(String grnCode, String grnType) {
		log.info("[markPutawayPending] Putaway pending received for grn {}, type {}", grnCode, grnType);
		if (GRNConstants.JIT_TYPE.equalsIgnoreCase(grnType)) {
			log.info("[markPutawayPending] Not calling putaway for jit type. grn {}, type {}", grnCode, grnType);
		} else {
			List<String> putawayCodes = grnItemDAO.getPutawayCodesByGrnCode(grnCode);
			putawayService.markPutawayPending(grnCode, putawayCodes);
			log.info("[markPutawayPending] Putaway pending is successful for grn {}, type {}", grnCode, grnType);
		}
	}

	private void syncGrnDataToInvoice(String grnCode, String facilityCode, List<GRNPIDMaster> grnpidMasterList, String grnType) throws Exception {
		try {
			log.info("[syncGrnDataToInvoice] Not Reconciling data for grnCode {}", grnCode);
			InvoiceSyncResponse invoiceSyncResponse = syncPidsToInvoice(grnCode, grnpidMasterList);
			log.info("[syncGrnDataToInvoice] Not Reconciling data for grnCode {}, invoiceSyncResponse {}",
					grnCode, invoiceSyncResponse);
			if (SUCCESS.equals(invoiceSyncResponse.getMeta().getMessage().toUpperCase())) {
				changeGrnStatusToClosed(grnCode, facilityCode, grnType);
			} else {
				log.error("[syncGrnDataToInvoice] Invoice API response for grn code " + grnCode + ": " + invoiceSyncResponse);
				throw new ApplicationException("Invoice update was unsuccessful : " + invoiceSyncResponse,
						GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
		} catch (Exception e){
			log.error("[syncGrnDataToInvoice] Close GRN: {}, {}", grnCode, e.getMessage());
			throw new ApplicationException("Unable to close invoice " + grnCode,
					GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	private void changeGrnStatusToClosed(String grnCode, String facilityCode, String grnType) {
		log.info("[changeGrnStatusToClosed] grnCode {}, facilityCode {}", grnCode, facilityCode);
		int closed = grnMasterDAO.closeGRN(grnCode, MDC.get("USER_ID"), facilityCode);
		if (closed <= 0)
			throw new ApplicationException("Update was unsuccessful", GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		if (isValidGrn(grnCode))
			saveToD365Table(grnCode);
	}

	private boolean isValidGrn(String grnCode) {
		log.info("[isValidGrn] Validating GRN {}", grnCode);
		try {
			List<GrnItemEntity> grnItemsList = grnItemDAO.getGrnItemsList(grnCode);
			log.info("[isValidGrn] Items fetched for GRN code {} : {}", grnCode, grnItemsList);
			if (CollectionUtils.isEmpty(grnItemsList) || grnItemsList.stream()
					.allMatch(grnItem -> StringUtils.equalsIgnoreCase(grnItem.getQcStatus(), "fail"))) {
				log.info("[isValidGrn] Blocking GRN as it is empty or all barcodes are QcFail {}", grnCode);
				return false;
			}
			String purchaseOrderDetail = grnD365Repository.getVendorLegalOwnerByGrnCode(grnCode);
			log.info("[isValidGrn] PO details fetched {} for GRN code {}", purchaseOrderDetail, grnCode);
			if(StringUtils.isEmpty(purchaseOrderDetail)) {
				log.info("[isValidGrn] Blocking GRN since PO data is not found for GRN code {}", grnCode);
				return false;
			}
			List<String> purchaseOrderDetails = Arrays.asList(purchaseOrderDetail.split(","));
			if (Objects.isNull(purchaseOrderDetails.get(0)) || Objects.isNull(purchaseOrderDetails.get(1)) || Objects.isNull(purchaseOrderDetails.get(2))) {
				log.info("[isValidGrn] Blocking GRN since validation data is missing for GRN code {}", grnCode);
				return false;
			}
			if(purchaseOrderDetails.get(0).equalsIgnoreCase(purchaseOrderDetails.get(2))) {
				log.info("[isValidGrn] Skipping grn {} as FOFO store GRN", grnCode);
				return false;
			}
			String vendorFacilityLegalOwner = poD365Repository.getVendorLegalOwnerByVendorId(purchaseOrderDetails.get(1));
			if (StringUtils.isEmpty(vendorFacilityLegalOwner)) {
				log.info("[isValidGrn] Creating GRN for external vendor with vendor ID {}", purchaseOrderDetails.get(1));
				return true;
			}
			log.info("[isValidGrn] Legal owner for vendor with vendor ID {} is {} ",
					 purchaseOrderDetails.get(1), vendorFacilityLegalOwner);
			return !(purchaseOrderDetails.get(0).equalsIgnoreCase(vendorFacilityLegalOwner));
		} catch (Exception e) {
			log.error("[isValidGrn] Error validating GRN code {}", grnCode, e);
		}
		return false;
	}

	public void checkEligibilityForIQC(String grnCode) {
		List<IqcGrnProductEntity> iqcGrnProductEntities = iqcGrnProductEntityService.findByGrnCode(grnCode);
		for (IqcGrnProductEntity iqcGrnProductEntity : iqcGrnProductEntities) {
			if (!IqcGrnProductStatusEnum.IQC_COMPLETE.equals(iqcGrnProductEntity.getStatus())) {
				log.error("Unable to close grn, grn box status should be IQC_COMPLETE before closing it grnCode {}, boxCode {}",
						grnCode, iqcGrnProductEntity.getBoxCode());
				throw new ApplicationException("Please mark grn " + grnCode + " box code " +
						iqcGrnProductEntity.getBoxCode() + " as IQC_COMPLETE before closing",
						GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
		}
	}

	public void pushCloseGrnCallInKafka(String grnCode, String facilityCode, Boolean reconcileData) {
		log.info("[pushCloseGrnCallInKafka] grnCode {}", grnCode);
		Map<String, Object> headers = new HashMap<>();
		headers.put("USER_ID", MDC.get("USER_ID"));
		headers.put("USER_MAIL", MDC.get("USER_MAIL"));
		headers.put("FACILITY_CODE", facilityCode);
		headers.put("RECONCILE_DATA", reconcileData);
		log.info("Producing a message to kafka producer for grn code {} with headers: {}", grnCode, headers);
		RetryUtils.sendMessage(grnCode, grnConfig.getTopicName(), headers);
	}

	public void validatePutawayCreatedForAllGrnItems(GRNMaster grnMaster, Integer closeGrnSyncUnsynedBarcodePutawayCount) {
		String grnCode = grnMaster.getGrnCode();
		try {
			log.info("[validatePutawayCreatedForAllGrnItems] grnCode {}, closeGrnSyncUnsynedBarcodePutawayCount {}",
					grnCode, closeGrnSyncUnsynedBarcodePutawayCount);
			String redisKey = "CLOSE_GRN_PUTAWAY_SYNC_RETRY_" + grnCode;
			checkIfRedisKeyExists(redisKey);

			List<GrnItemEntity> missingPutawayGrnItemList = grnItemEntityService.findByGrnCodeAndPutawayCodeIsNull(grnCode);
			if (missingPutawayGrnItemList.size() == 0) return;
			if (missingPutawayGrnItemList.size() > closeGrnSyncUnsynedBarcodePutawayCount) {
				log.info("[validatePutawayCreatedForAllGrnItems] Push item to kafka for {}", grnCode);
				pushGrnItemsToKafkaToCreatePutawayList(grnCode, grnMaster.getUnicomGrnCode(), missingPutawayGrnItemList);
				log.info("[validatePutawayCreatedForAllGrnItems] Grn item pushed to kafka for grncode {}", grnCode);
				saveRedisKeyToCloseGrnPutawaySyncRetry(redisKey);
				throw new ApplicationException("GRN closure processing in progress. Please retry in "
						+ grnConfig.getCloseGrnRetryToSyncBarcodeInPutawayTime() + " minutes.", GRNExceptionStatus.GRN_BAD_REQUEST);
			} else {
				log.info("[validatePutawayCreatedForAllGrnItems] Async call to putaway service {}", grnCode);
				List<String> failedBarcodeList = new ArrayList<>();
				createGrnItemPutaway(grnCode, grnMaster.getUnicomGrnCode(), missingPutawayGrnItemList, failedBarcodeList);
				log.info("[validatePutawayCreatedForAllGrnItems] Success call to putaway service API for grn code {}," +
						" failedBarcodeList {}", grnCode, failedBarcodeList);
				if (!failedBarcodeList.isEmpty())
					throw new ApplicationException("Putaway incomplete: Delete " + failedBarcodeList.toString() +
							" from GRN", GRNExceptionStatus.GRN_BAD_REQUEST);
			}
		} catch (Exception e) {
			log.error("[validatePutawayCreatedForAllGrnItems] grnCode {}: error {} ", grnCode, e.getMessage());
			throw new ApplicationException("Unable to create putaway for all grn items: " + e.getMessage(),
					GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	private void saveRedisKeyToCloseGrnPutawaySyncRetry(String redisKey) throws Exception {
		Date now = new Date();
		long newTime = now.getTime() + ((long) grnConfig.getCloseGrnRetryToSyncBarcodeInPutawayTime() * 60 * 1000);
		now.setTime(newTime);
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String dateString = dateFormat.format(now);
		log.info("[saveRedisKeyToCloseGrnPutawaySyncRetry] currentTime {}, dateString {}, newTime {}", now, dateString, newTime);
		RedisHandler.redisOps(RedisOps.SETVALUETTL, redisKey, dateString, 1L, TimeUnit.HOURS);
		log.info("[saveRedisKeyToCloseGrnPutawaySyncRetry] Key successfully created {} currentTime {}," +
				" newTime {}", redisKey, now, newTime);
	}

	private void checkIfRedisKeyExists(String redisKey) throws Exception {
		if (checkRedisKey(redisKey)) {
			String dateString = (String) RedisHandler.redisOps(RedisOps.GET, redisKey);
			log.info("[checkIfRedisKeyExists] Redis key '{}' redisValue: {}", redisKey, dateString);
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Date redisKeyDate = dateFormat.parse(dateString);
			Date now = new Date();
			log.info("[checkIfRedisKeyExists] Redis key '{}' exists with value: {}", redisKey, redisKeyDate);
			if (now.compareTo(redisKeyDate) < 0) {
				log.error("[checkIfRedisKeyExists] Redis key '{}' exists, and current time is before stored time. " +
						"Current Time: {}, Stored Time: {}", redisKey, now, redisKeyDate);
				throw new ApplicationException("GRN closure processing is still in progress. Please wait.",
						GRNExceptionStatus.GRN_BAD_REQUEST);
			}
		}
	}

	private boolean checkRedisKey(String redisKey) throws Exception {
		try{
			return RedisHandler.hasKey(redisKey);
		} catch (Exception e){
			log.error("[checkRedisKey] Error while fetching redis key redisKey {}, error {}", redisKey, e.getMessage());
			return false;
		}
	}

	public void pushGrnItemsToKafkaToCreatePutawayList(String grnCode, String unicomGrnNum, List<GrnItemEntity> missingPutawayGrnItemList) {
		try {
			log.info("[pushGrnItemsToKafkaToCreatePutawayList] grnCode {}", grnCode);
			List<Future<?>> addItemToPutaway = new ArrayList<>();
			for (GrnItemEntity entity : missingPutawayGrnItemList) {
				addItemToPutaway.add(pushGrnItemsToKafkaToCreatePutaway(grnCode, unicomGrnNum, entity));
			}
			this.checkAllTasksCompleted(addItemToPutaway);
			log.info("[pushGrnItemsToKafkaToCreatePutawayList] grncode {}, items {}", grnCode, missingPutawayGrnItemList);
		} catch (Exception e) {
			log.error("[pushGrnItemsToKafkaToCreatePutawayList] grnCode {}: error {}", grnCode, e.getMessage());
			throw new ApplicationException("Unable to push grn item for " + grnCode +
					" to kafka putaway creation: " + e.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	@Async("closeGrnCreateGrnItemPutawayExecutor")
	private CompletableFuture<Boolean> pushGrnItemsToKafkaToCreatePutaway(String grnCode, String unicomGrnNum,
																		  GrnItemEntity entity) {
		try {
			log.info("[pushGrnItemsToKafkaToCreatePutaway] Async createPutaway executed for grnCode: {}, " +
							"barcode {}, missingPutawayGrnItemList {}", grnCode, entity.getBarcode(), entity);
			putawayService.pushGrnItemsToKafkaToCreatePutaway(grnCode, unicomGrnNum, entity);
			return CompletableFuture.completedFuture(true);
		} catch (Exception e) {
			log.error("[pushGrnItemsToKafkaToCreatePutaway] Async createPutaway executed for grnCode: {}, missingPutawayGrnItemList {}, error {}"
					, grnCode, entity, e.getMessage());
			return CompletableFuture.completedFuture(true);
		}
	}

	private void createGrnItemPutaway(String grnCode, String unicomGrnNum,
									  List<GrnItemEntity> missingPutawayGrnItemList, List<String> failedBarcodeList) {
		try {
			log.info("[createPutaway] Async createPutaway executed for grnCode: {}, missingPutawayGrnItemList {}",
					grnCode, missingPutawayGrnItemList);
			List<CreatePutawayResponse> response = putawayService.createGrnItemPutaway(grnCode, unicomGrnNum,
					missingPutawayGrnItemList);
			Map<String, Integer> barcodePutawayMapping = createBarcodePutawayMapping(response);
			savePutawayCodeDetailsInGrnItemEntity(barcodePutawayMapping, missingPutawayGrnItemList, failedBarcodeList);
			grnItemEntityService.saveOrUpdateAll(missingPutawayGrnItemList);
		} catch (Exception e) {
			log.error("[createPutaway] Async createPutaway executed for grnCode: {}, error {}"
					, grnCode, e.getMessage());
			throw new ApplicationException(e.getMessage(), GRNExceptionStatus.GRN_BAD_REQUEST);
		}
	}

	private Map<String, Integer> createBarcodePutawayMapping(List<CreatePutawayResponse> createPutawayResponses) {
		try {
			log.info("[createBarcodePutawayMapping] createBarcodePutawayMapping {}", createPutawayResponses);
			Map<String, Integer> response = new HashMap<>();
			for (CreatePutawayResponse createPutawayResponse : createPutawayResponses) {
				for (CreatePutawayItemResponse res : createPutawayResponse.getBarcodeInfo()) {
					if (StringUtils.isNotBlank(res.getNewPutawayId())) {
						response.put(res.getBarcodeNumber(), Integer.parseInt(res.getNewPutawayId()));
					}
				}
				log.info("[createBarcodePutawayMapping] Close Grn {} Create putaway createBarcodePutawayMapping {}",
						createPutawayResponse.getGrnNumber(), createPutawayResponse);
			}
			String json = new Gson().toJson(response);
			return response;
		} catch (Exception e){
			log.error("[createBarcodePutawayMapping] createBarcodePutawayMapping {}, error {}",
					createPutawayResponses, e.getMessage());
			throw new ApplicationException(e.getMessage(), GRNExceptionStatus.GRN_BAD_REQUEST);
		}
	}

	private void savePutawayCodeDetailsInGrnItemEntity(Map<String, Integer> barcodePutawayMapping,
													   List<GrnItemEntity> missingPutawayGrnItemList, List<String> failedBarcodeList) {
		for (GrnItemEntity entity : missingPutawayGrnItemList) {
			String barcode = entity.getBarcode();
			if (barcodePutawayMapping.containsKey(barcode)) {
				Integer putawayCode = barcodePutawayMapping.get(barcode);
				entity.setPutawayCode(putawayCode);
				entity.setUpdatedAt(new Date());
				log.info("[setPutawayCodeForGrnItem] Putaway code successfully stored grnNum {}, Barcode {} ",
						entity.getGrnCode(), barcode);
			} else {
				log.error("[setPutawayCodeForGrnItem] Putaway not created for {}", barcode);
				failedBarcodeList.add(barcode);
			}
		}
	}

	private void checkAllTasksCompleted(List<Future<?>> tasks) throws InterruptedException, ExecutionException {
		while (!closeGrnCreateGrnItemPutawayHandler.isQueueEmpty()) {
			closeGrnCreateGrnItemPutawayHandler.processRejectedTasks();
		}
		for (Future<?> task : tasks) {
			if (!task.isDone()) {
				task.get();
			}
		}
	}

	@Logging
	public void validateGRNItemCountWithPoAndInvoice(String grnCode, String grnType, String poNum) throws Exception {
		log.info("[validateGRNItemCountWithPoAndInvoice] CLOSE_GRN Validate Po Invoice Count for GRN {}, poNum {}",
				grnCode, poNum);
//		List<GrnItemPoInvoiceCount> invoiceResultList = grnItemDAO.getInvoiceCountForGrnPid(grnCode);
//		List<GrnItemPoInvoiceCount> poResultList = grnItemDAO.getPoCountForGrnPid(grnCode);
		List<GrnPOInvoicePidData> pidPOInvoiceForGrn = grnItemDAO.getPidPOInvoiceForGrn(grnCode);
		log.info("[validateGRNItemCountWithPoAndInvoice] CLOSE_GRN for pidPOInvoiceForGrn:{}", pidPOInvoiceForGrn);
		if (!CollectionUtils.isEmpty(pidPOInvoiceForGrn)) {
			List<String> pids =
					pidPOInvoiceForGrn.stream().map(grnPOInvoicePidData -> grnPOInvoicePidData.getPid()).collect(Collectors.toList());
			List<GrnItemPoInvoiceCount> poResultList = grnItemDAO.getPoCountForGrnPid(grnCode,
					pidPOInvoiceForGrn.get(0).getPoId(), pids);
			List<GrnItemPoInvoiceCount> extraQtyResultList = new LinkedList<>();
			List<String> pidList = new LinkedList<>();
			for (GrnItemPoInvoiceCount grnPid : poResultList) {
				if (grnPid.getScannedQty() != null && grnPid.getPoQty() != null && grnPid.getScannedQty() > grnPid.getPoQty()) {
					if (!pidList.contains(grnPid.getPid()))
						pidList.add(grnPid.getPid());
					extraQtyResultList.add(grnPid);
				}
			}

			String invoiceLevel = getInvoiceLevelFromPoNumber(poNum);
			log.info("[validateGRNItemCountWithPoAndInvoice] GRN {}, poNum {}, invoiceLevel {}",
					grnCode, poNum, invoiceLevel);
			if(InvoiceLevelEnum.SUMMARY.name().equalsIgnoreCase(invoiceLevel)){
				log.info("[validateGRNItemCountWithPoAndInvoice] GRN {} is of SUMMARY Level", grnCode);
				PurchaseInvoiceEntity invoiceEntity =
						purchaseInvoiceEntityService.getInvoiceByReferenceNumber(
								Integer.valueOf(pidPOInvoiceForGrn.get(0).getInvoiceRefNum()), true);
				long totalCount = grnCacheServiceUtils.getCLInvoiceCountFromDB(pidPOInvoiceForGrn.get(0).getInvoiceRefNum());
				double totalPrice = grnCacheServiceUtils.getCLInvoicePriceFromDB(grnCode,
						pidPOInvoiceForGrn.get(0).getInvoiceRefNum(), pidPOInvoiceForGrn.get(0).getPoId());
				log.info("[validateGRNItemCountWithPoAndInvoice] Total qty and price for grnCode {}, grn totalCount {}, grn totalPrice {}," +
								" invoice qty {} and price {}",
                        grnCode, totalCount, totalPrice, invoiceEntity.getTotalInvoiceQty(),
						invoiceEntity.getTotalInvoiceAmount());
				if(totalCount > invoiceEntity.getTotalInvoiceQty() || totalPrice > invoiceEntity.getTotalInvoiceAmount()){
				    log.error("[validateGRNItemCountWithPoAndInvoice] " +
							"Close Grn Total count or price can not be greater than Invoice qty or price");
                    throw new ApplicationException("Close Grn Total count or price can not be greater than Invoice qty or price",
                            GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
				}
			}else{
				List<GrnItemPoInvoiceCount> invoiceResultList = grnItemDAO.getInvoiceCountForGrnPid(grnCode,
						pidPOInvoiceForGrn.get(0).getInvoiceRefNum(), pids);
				log.info("[validateGRNItemCountWithPoAndInvoice] CLOSE_GRN invoiceResultList: {}, poResultList:{}", invoiceResultList.toString(),
						poResultList.toString());
				for (GrnItemPoInvoiceCount grnPid : invoiceResultList) {
					if (grnPid.getScannedQty() != null && grnPid.getInvoiceQty() != null && grnPid.getScannedQty() > grnPid.getInvoiceQty()) {
						pidList.add(grnPid.getPid());
						extraQtyResultList.add(grnPid);
					}
				}
			}

			log.info("[validateGRNItemCountWithPoAndInvoice] CLOSE_GRN PO/Invoice Item Count {}", extraQtyResultList.toString());
			if (!pidList.isEmpty()) {
				log.error("[validateGRNItemCountWithPoAndInvoice] CLOSE_GRN Grn item count is greater than Po/Invoice count for grnCode: {}, pid: {}, " +
								"extraQtyResultList: {}",
						grnCode, pidList.toString(), extraQtyResultList.toString());
                throw new ApplicationException("Grn item count is greater than Po/Invoice count for pid " + pidList.toString(),
                        GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
		}
	}

	private String getInvoiceLevelFromPoNumber(String po_num) throws CustomException {
		log.info("[getInvoiceLevelFromPoNumber] poNum {}", po_num);
		com.lenskart.nexs.common.entity.po.PurchaseOrder purchaseOrder = poInvoiceService.getPurchaseOrder(po_num);
		log.info("[getInvoiceLevelFromPoNumber] poNum {}, dbResult {}", po_num, purchaseOrder.getInvoiceLevel());
		return purchaseOrder.getInvoiceLevel();
	}

	@Logging
	public void closeGRNEvent(String grnCode, String facilityCode, GRNMaster grnMaster) throws Exception {
		log.info("CLOSE_GRN_EVENT Close Grn for Grn code {}", grnCode);
		Map<String, Object> headers = new HashMap<>();
		headers.put("USER_ID", grnMaster.getUpdatedBy());
		headers.put("USER_MAIL", "<EMAIL>");
		headers.put("FACILITY_CODE", grnMaster.getFacility());
		log.info("CLOSE_GRN_EVENT Producing a message to kafka producer for grn code {} with headers: {}", grnCode, headers);
		RetryUtils.sendMessage(grnCode, grnConfig.getTopicName(), headers);
	}

	private void checkPidStatuses(List<GRNPIDMaster> grnPidMasters) {
		for (GRNPIDMaster grnpidMaster : grnPidMasters) {
			if (FAILED.equals(grnpidMaster.getPidStatus()) && grnpidMaster.getManualOverride() == 0)
				throw new ApplicationException("One of the pid in blocked state, manual intervention required", GRNExceptionStatus.GRN_QC_FAILED);
		}
	}

	public void releaseEmptyBoxes(String grnCode, String facilityCode) {
		log.info("Empty box-mapping release for grn : " + grnCode);
		List<BoxMapping> emptyBoxMappings = grnItemDAO.getEmptyBoxMappings(grnCode);
		emptyBoxMappings = GRNUtils.getMappingsWithUpdatedBy(emptyBoxMappings);
		log.info("Empty box-mappings are : " + emptyBoxMappings);
		boolean deleted = false;
		if (emptyBoxMappings.size() > 0) {
			List<Box> emptyBoxes = GRNUtils.getBoxes(emptyBoxMappings, facilityCode);
			deleted = grnItemDAO.releaseEmptyBoxes(emptyBoxMappings, emptyBoxes);
			for (BoxMapping boxMapping : emptyBoxMappings)
				grnQcLogService.logGRNQcAction(boxMapping.getGrnCode(), boxMapping.getPid(), boxMapping.getBarcode(),
						GRNQcLogEvents.RELEASE);
		}
		if (deleted)
			log.info("BoxMappings released : " + emptyBoxMappings);
	}

	public void redisCleanUp(String grnCode, List<GRNPIDMaster> pids) throws Exception {
		log.info("Redis cleanup for grnCode : " + grnCode);
		Set<String> keys = new HashSet<>();
		String outerTemplate = String.format("%s%s%s", grnConfig.getKeyPrefix(), grnCode, DEL);
		for(GRNPIDMaster pid : pids) {
			String innerTemplate = String.format("%s%s%s", outerTemplate, pid.getPid(), DEL);
			keys.add(innerTemplate + QC_CONFIG);
			keys.add(innerTemplate + TOTAL_SCANNED);
			keys.add(innerTemplate + TOTAL_FAILED);
			keys.add(innerTemplate + STATUS);
			keys.add(innerTemplate + GRN_MANUAL_OVERRIDE);
			keys.add(innerTemplate + BOX_REQUIRED);
			keys.add(innerTemplate + ESTIMATED_QTY);
			keys.add(innerTemplate + SCANNED);
			keys.add(innerTemplate + FAILED);
			keys.add(innerTemplate + TOTAL_SCANNED_COUNT);
		}
		log.info("keys to be removed from the redis : " + keys);
		stringRedisTemplate.delete(keys);
	}

	public InvoiceSyncResponse syncPidsToInvoice(String grnCode, List<GRNPIDMaster> grnpidMasterList) throws Exception {
		log.info("[syncPidsToInvoice] Sync the pid data to invoice system for grn code " + grnCode);
		InvoiceSyncDTO invoiceSyncDTO = null;
		GRNMaster grnMaster = null;
		if(!grnpidMasterList.isEmpty()) {
			List<String> pids = grnpidMasterList.stream().map(x -> x.getPid()).collect(Collectors.toList());
			Map<String, Object> countMap = grnItemDAO.getGRNScanCountByPID(grnCode);
			//Map<String, Integer> failCountMap = grnItemDAO.getCountByPIDStatus(grnCode, pids, QC_FAIL);
			//Map<String, Integer> passCountMap = grnItemDAO.getCountByPIDStatus(grnCode, pids, QC_PASS);
			invoiceSyncDTO = getInvoiceSync(pids, countMap);
			invoiceSyncDTO.setInvoiceRefNum(!grnpidMasterList.isEmpty() ? grnpidMasterList.get(0).getInvoiceReferenceNum() : null);
		} else {
			grnMaster = grnMasterDAO.getGRNMaster(grnCode, MDC.get("USER_ID"));
			invoiceSyncDTO = new InvoiceSyncDTO();
			invoiceSyncDTO.setItems(new ArrayList<>());
			if(grnpidMasterList.isEmpty() && grnMaster == null)
				throw new ResponseStatusException(HttpStatus.NOT_FOUND, "No GRN Record found to close");
			invoiceSyncDTO.setInvoiceRefNum(grnMaster.getInvoice().getInvoiceRefNum());
		}
		invoiceSyncDTO.setGrnNum(grnCode);
		log.info("[syncPidsToInvoice] invoiceSyncDTO {}", invoiceSyncDTO);
		InvoiceSyncResponse invoiceSyncResponse = RetryUtils.postData(
				grnConfig.getInvoiceBaseUrl() + grnConfig.getInvoicePidSyncUrl(), invoiceSyncDTO, InvoiceSyncResponse.class);
		log.info("Response from invoice close grn : " + invoiceSyncResponse);
		return invoiceSyncResponse;
	}

	public InvoiceSyncDTO getInvoiceSync(List<String> pids, Map<String, Object> countMap) {
		InvoiceSyncDTO invoiceSyncDTO = new InvoiceSyncDTO();
		List<InvoiceSyncItem> list = new ArrayList<>();
		for(String pid : pids) {
			Map<String, Object> pidCountMap = (Map<String, Object>) countMap.getOrDefault(pid, null);
			InvoiceSyncItem invoiceSyncItem = new InvoiceSyncItem();
			invoiceSyncItem.setPid(pid);
			int totalCount = pidCountMap != null ? (int) pidCountMap.getOrDefault("total_count", 0) : 0;
			int failCount = pidCountMap != null ? (int) pidCountMap.getOrDefault("fail_count", 0) : 0;
			invoiceSyncItem.setAcceptedQty(totalCount - failCount);
			invoiceSyncItem.setRejectedQty(failCount);
			if (invoiceSyncItem.getAcceptedQty() + invoiceSyncItem.getRejectedQty() > 0)
				list.add(invoiceSyncItem);
		}
		invoiceSyncDTO.setItems(list);
		log.info("[getInvoiceSync] invoiceSyncDTO {}", invoiceSyncDTO);
		return invoiceSyncDTO;
	}

	@Logging
	public void closeAllGRN(String invoiceRefNum, String facilityCode) throws Exception {
		try {
			log.info("[closeAllGRN] Close All GRn for Invoice Ref Num {}, facilityCode {}", invoiceRefNum, facilityCode);

			List<GrnMasterEntity> grnMasterEntities = grnMasterDAO.getOpenGRNListByInvoice(invoiceRefNum, facilityCode);
			List<String> grnList = grnMasterEntities.stream().map(GrnMasterEntity::getGrnCode).collect(Collectors.toList());
			log.info("[closeAllGRN] invoiceRefNum {}, grnList {} ", invoiceRefNum, grnList);
			if (grnMasterEntities.isEmpty())
				throw new ResponseStatusException(HttpStatus.NOT_FOUND, "No open GRNs found for invoice : " + invoiceRefNum);

			String poNum = grnMasterEntities.get(0).getPoId();
			List<GRNPIDMaster> grnpidMasterList = grnPidDao.getGRNPIDS(grnList);
			checkPidStatuses(grnpidMasterList);
			log.info("[closeAllGRN] invoiceRefNum {}, grnList {} All pid status checked", invoiceRefNum, grnList);

			int reconcileGrnCount = 1;
			int grnMasterEntitiesSize = grnMasterEntities.size();
			for (GrnMasterEntity grnMaster : grnMasterEntities) {
				String grnCode = grnMaster.getGrnCode();
				String type = grnMaster.getGrnType();
				log.info("Close GRN for invoiceRefNum {}, Grn code {}", invoiceRefNum, grnMaster);
				checkEligibilityForIQCFromInvoice(grnCode, grnMaster.getInvoiceId());
				validateGRNItemCountWithPoAndInvoice(grnCode, type, poNum);
				markPutawayPending(grnCode, grnMaster.getGrnType());

				putawayService.closeGRN(grnCode);
				List<GRNPIDMaster> pidList = grnpidMasterList.stream().filter(grnpidMaster -> grnpidMaster.getGrnCode().equals(grnCode)).collect(Collectors.toList());

				log.info("[closeGRN] closeGrnReconcileExcludeCategory {}, grn type {}",
						closeGrnReconcileExcludeCategory, grnMaster.getGrnType());
				if (closeGrnReconcileExcludeCategory != null &&
						(closeGrnReconcileExcludeCategory.contains(grnMaster.getGrnType()) ||
								closeGrnReconcileExcludeCategory.contains("ALL"))) {
					log.info("[closeGRN] Not Reconciling data for grnCode {}", grnCode);
					syncGrnDataToInvoice(grnCode, facilityCode, pidList, grnMaster.getGrnType());
					pushCloseGrnCallInKafka(grnCode, grnMaster.getFacility(), false);
				} else {
					log.info("[closeGRN] Reconciling data for grnCode {}", grnCode);
					changeGrnStatusToClosed(grnCode, facilityCode, grnMaster.getGrnType());
					pushCloseGrnCallInKafka(grnCode, grnMaster.getFacility(), reconcileGrnCount == grnMasterEntitiesSize);
					++reconcileGrnCount;
				}

				if (!grnpidMasterList.isEmpty())
					redisCleanUp(grnCode, grnpidMasterList);
			}

			log.info("[closeAllGRN] invoiceRefNum {}, grnList {} All grn closed successfully", invoiceRefNum, grnList);
		} catch (Exception e){
			log.error("[closeAllGRN] invoiceRefNum: {}, {}", invoiceRefNum, e.getMessage());
			throw new ApplicationException("Unable to close all grn for " + invoiceRefNum,
					GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	@Logging
	public void assignGRN(UserActivity userActivity) throws Exception {

		GRNMaster grnMaster = grnMasterDAO.getGRNMaster(userActivity.getGrnCode());
		if(grnMaster != null && MDC.get("FACILITY_CODE") != null && grnMaster.getFacility() != null && !grnMaster.getFacility().equals(MDC.get("FACILITY_CODE"))) {
			throw new Exception("Incorrect facility chosen in header");
		}
		if(GRN_STATUS_CLOSED.equalsIgnoreCase(grnMaster.getGrnStatus())) {
			throw new ResponseStatusException(HttpStatus.EXPECTATION_FAILED, "GRN is in closed state, cannot be assigned to other user");
		}

		userActivity.setAction(USER_ACTION_ASSIGNMENT);
		userActivityDAO.assignGRN(userActivity);
	}

	@Logging
	public GRNAddPidResponseDTO addGrnPID(CreateGRNPIDTO createGRNPIDTO, String facilityCode) throws Exception {
		log.info("[addGrnPID] createGRNPIDTO {}, facilityCode {}", createGRNPIDTO, facilityCode);
		validatePID(createGRNPIDTO.getMeta().getInvoice(), createGRNPIDTO.getPid(), facilityCode);

		log.info("Checking if grn is already closed : {}", createGRNPIDTO.getGrnCode());
		GRNMaster grnMaster = grnMasterDAO.getGRNMaster(createGRNPIDTO.getGrnCode());

		if(grnMaster != null && MDC.get("FACILITY_CODE") != null && grnMaster.getFacility() != null && !grnMaster.getFacility().equals(MDC.get("FACILITY_CODE"))) {
			throw new Exception("Incorrect facility chosen in header");
		}

		if (grnMaster != null && StringUtils.equalsIgnoreCase(grnMaster.getGrnStatus(), GRN_STATUS_CLOSED)) {
			throw new ResponseStatusException(HttpStatus.EXPECTATION_FAILED, "GRN is already closed");
		}

		log.info("[addGrnPID] Deleting invoice and po redis counter for invoice {}, po_id {} and pid {}", createGRNPIDTO.getMeta().getInvoice().getInvoiceRefNum(),
				createGRNPIDTO.getMeta().getPo().getPoId(), createGRNPIDTO.getPid().getPid());
		grnItemService.deleteRedisCountKeyForInvoiceAndPO(createGRNPIDTO.getMeta().getInvoice().getInvoiceRefNum(),
				createGRNPIDTO.getMeta().getPo().getPoId(), createGRNPIDTO.getPid().getPid());

		log.info("[addGrnPID] grnCode {}, invoiceLevel {}", createGRNPIDTO.getGrnCode(), createGRNPIDTO.getInvoiceLevel());
		if(isAllItemScanned(createGRNPIDTO.getMeta().getInvoice(), createGRNPIDTO.getPid().getPid(),
				createGRNPIDTO.getType(), createGRNPIDTO.getInvoiceLevel(), createGRNPIDTO.getMeta().getPo()))
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Already scanned all the items of pid : " + createGRNPIDTO.getPid().getPid());

//		if (isEstimatedQtyMoreThanInvoiceQty(createGRNPIDTO.getMeta().getInvoice(), createGRNPIDTO.getPid().getPid(),
//				createGRNPIDTO.getPid().getEstimatedQuantity()))
//			throw new ResponseStatusException(HttpStatus.EXPECTATION_FAILED, "Estimated quantity is greater than total invoice quantity");

		GRNMaster grnMasterInDB = grnMasterDAO.getGRNMasterNP(createGRNPIDTO.getGrnCode(), MDC.get("USER_ID"));

		if (grnMasterInDB == null)
			throw new ResponseStatusException(HttpStatus.NOT_FOUND, "No record found for grnCode : " + createGRNPIDTO.getGrnCode());

		Map<String, Boolean> res = grnPidDao.checkFailedGRNPID(createGRNPIDTO.getMeta().getInvoice().getInvoiceRefNum(), createGRNPIDTO.getPid().getPid());

		if(res.get("isFailed") && !res.get("manualOverride")) {
			throw new ResponseStatusException(HttpStatus.EXPECTATION_FAILED, "Given pid has entered failed state for this invoice");
		}

		Map<String, Object> productAttributesMapping = new HashMap<>();
		if(InvoiceLevelEnum.SUMMARY.name().equalsIgnoreCase(createGRNPIDTO.getInvoiceLevel())){
			PurchaseOrderItem purchaseOrderItem = purchaseOrderItemEntityService.
					getPurchaseOrderItemByPoNumAndPid(createGRNPIDTO.getMeta().getPo().getPoId(),
							Integer.parseInt(createGRNPIDTO.getPid().getPid()));
			ObjectMapper mapper = new ObjectMapper();
			productAttributesMapping =
					mapper.readValue(purchaseOrderItem.getProductAttributes(), new TypeReference<Map<String, Object>>() {
					});
		}
		GRNPIDMaster grnpidMaster = GRNUtils.getGRNPIDMaster(
				createGRNPIDTO, (short) (res.get("manualOverride") ? 1 : 0), productAttributesMapping);

		GRNPidDetailsDTO grnPidInfo = grnPidMasterService.getGRNPidDetails(grnpidMaster);
		if(grnPidInfo != null && grnPidInfo.getIsBoxRequired() != null && grnPidInfo.getIsBoxRequired()) {
			cacheDAO.setBoxRequired(grnpidMaster.getGrnCode(), grnpidMaster.getPid());
		}

		if(grnpidMaster.getManualOverride() == 1) {
			GRNPidListDTO grnPidListDTO = new GRNPidListDTO();
			grnPidListDTO.setGrnCode(grnpidMaster.getGrnCode());
			grnPidListDTO.setPid(grnpidMaster.getPid());
			qcStatusDAO.setManualOverrideForList(Arrays.asList(grnPidListDTO));
		}

		boolean added = grnPidDao.createGRNPIDMaster(grnpidMaster);
		if (!added)
			throw new ApplicationException("Add PID was unsuccessful", GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		grnMasterInDB = grnMasterDAO.getGRNMaster(createGRNPIDTO.getGrnCode(), MDC.get("USER_ID"));
		GRNItem grnItem = GRNItemServiceUtils.getGRNItem(createGRNPIDTO, productAttributesMapping);
		grnCacheService.loadConfig(grnItem);

		log.info("[addGrnPID] Checking GRN type {}, pId {}, type {}, invoiceLevel {}",
				createGRNPIDTO.getGrnCode(), createGRNPIDTO.getPid(), createGRNPIDTO.getType(), createGRNPIDTO.getInvoiceLevel());
		if(InvoiceLevelEnum.SUMMARY.name().equalsIgnoreCase(createGRNPIDTO.getInvoiceLevel())) {
			BaseResponseModel response = RetryUtils.postData(
					grnConfig.getInvoiceBaseUrl() + grnConfig.getCreateInvoiceItemCl()
							+ "?vendor_invoice_num=" + grnMaster.getInvoice().getInvoiceId()
							+ "&pid=" + createGRNPIDTO.getPid().getPid()
							+ "&po_num=" + createGRNPIDTO.getMeta().getPo().getPoId(),
					null, BaseResponseModel.class);
			log.info("Response from createInvoiceCl : " + response);
			if (!response.getData().toString().contains("Purchase invoice item successfully created")){
				log.error("[addGrnPID] Unable to create purchase invoice item {}, pid {}, error {}",
						createGRNPIDTO.getGrnCode(), createGRNPIDTO.getPid(), response.getData());
				throw new Exception("Unable to create purchase invoice item " + response.getData());
			}
		}

		GRNMasterMetaDTO grnMasterMetaDTO =  GRNMasterMetaDTOMapper.INSTANCE.mapToGRNMasterMetaDTO(grnMasterInDB);
		if(GRN_STATUS_CREATED.equalsIgnoreCase(grnMasterInDB.getGrnStatus())) {
			grnMasterInDB.setGrnStatus(GRN_STATUS_IN_PROGRESS);
			grnMasterInDB.setUpdatedBy(MDC.get("USER_ID"));
			grnMasterInDB.setUpdatedAt(new Timestamp(System.currentTimeMillis()));

			grnMasterDAO.updateGRN(grnMasterInDB);
		}
		imsService.updateLandedCostDetails(grnMaster.getPoId(), createGRNPIDTO.getPid().getPid());

		log.info("[addGrnPID] boxBarcodeDisableLegalOwnerList {}, legal owner {}", boxBarcodeDisableLegalOwnerList, grnMasterMetaDTO.getInvoice().getLegalOwner());
		if(boxBarcodeDisableLegalOwnerList.contains(grnMasterMetaDTO.getInvoice().getLegalOwner())){
			log.info("[addGrnPID] Setting box barcode required as false");
			grnPidInfo.setIsBoxRequired(false);
		}
		return GRNUtils.getGRNAddPidResponseDTO(grnMasterMetaDTO, grnMasterInDB, grnPidInfo, grnMasterInDB.getGrnStatus());
	}

	private boolean isAllItemScanned(Invoice invoice, String pid, String type, String invoiceLevel, PurchaseOrder po) {
		log.info("[isAllItemScanned] invoice no {}, pid {}, type {}, po {}, invoiceLevel {}",
				invoice.getInvoiceRefNum(), pid ,type, po, invoiceLevel);
		long itemScanned = 0;
		try {
			itemScanned = qcStatusDAO.getInvoiceQtyCount(invoice.getInvoiceRefNum(), pid);
			log.info("[isAllItemScanned] invoice no {}, itemScanned {}", invoice.getInvoiceRefNum(), itemScanned);
		} catch (ResponseStatusException ex) {
			log.info("Probably no item scan has happened for invoice : " + invoice.getInvoiceRefNum());
		}

		if(InvoiceLevelEnum.SUMMARY.name().equalsIgnoreCase(invoiceLevel)){
			log.info("[isAllItemScanned] SUMMARY Invoice invoice no {}, itemScanned {}", invoice.getInvoiceRefNum(), itemScanned);
			List<POProduct> pids = po != null ? po.getPids() : new ArrayList<>();
			log.info("[isAllItemScanned] SUMMARY Invoice invoice no {}, itemScanned {}, pids {}", invoice.getInvoiceRefNum(), itemScanned, pids);
			if (pids != null) {
				for (POProduct product : pids) {
					if (pid.equalsIgnoreCase(product.getPid())){
						log.info("[isAllItemScanned] SUMMARY Invoice invoice no {}, itemScanned {}, product qunatity {}",
								invoice.getInvoiceRefNum(), itemScanned, product.getQuantity());
						return itemScanned >= product.getQuantity();
					}
				}
			}
		} else {
			log.info("[isAllItemScanned] ITEM Invoice invoice no {}, itemScanned {}", invoice.getInvoiceRefNum(), itemScanned);
			List<Product> pids = invoice != null ? invoice.getPids() : new ArrayList<>();
			log.info("[isAllItemScanned] ITEM Invoice invoice no {}, itemScanned {}, pids {}", invoice.getInvoiceRefNum(), itemScanned, pids);
			if (pids != null) {
				for (Product product : pids) {
					if (pid.equalsIgnoreCase(product.getPid())){
						log.info("[isAllItemScanned] ITEM Invoice invoice no {}, itemScanned {}, product quantity {}",
								invoice.getInvoiceRefNum(), itemScanned, product.getQuantity());
						return itemScanned >= product.getQuantity();
					}
				}
			}
		}
		log.info("[isAllItemScanned] True Invoice itemScanned {}", itemScanned);
		return true;
	}

//	public Map<String, VendorSTDResponseDTO> getVendorSTDResponse(GRNMaster grnMaster) throws JsonProcessingException {
//		log.info("Get vendor config information for grn master : " + grnMaster);
//		List<VendorSTDInfoDTO> vendorInfoList = GRNUtils.getVendorInfoList(grnMaster);
//		TypeReference<Map<String, VendorSTDResponseDTO>> typeRef = new TypeReference<Map<String, VendorSTDResponseDTO>>() {};
//		ResponseDTO<Map<String, VendorSTDResponseDTO>> responseDTO = RetryUtils.postData(grnConfig.getBaseUrl() +
//				grnConfig.getVendorStandardInfoUrl(), vendorInfoList, ResponseDTO.class);
//		if(responseDTO == null){
//			log.error("Vendor STD API responded with null");
//			throw new ApplicationException("Internal API Error", GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
//		}
//		ObjectMapper mapper = new ObjectMapper();
//		Map<String, VendorSTDResponseDTO> result = null;
//		try {
//			result = mapper.readValue(mapper.writeValueAsString(responseDTO.getResult()), typeRef);
//		} catch (JsonProcessingException ex) {
//			log.error("Exception while parsing vendor standard response : ", ex);
//			throw ex;
//		}
//		return result;
//
//	}

//	@Logging
//	public boolean isEstimatedQtyMoreThanInvoiceQty(Invoice invoice, String pid, Long estimatedQty) {
//
//		List<Product> pids = invoice != null ? invoice.getPids() : new ArrayList<>();
//		if (pids != null) {
//			for (Product product : pids) {
//				if (pid.equalsIgnoreCase(product.getPid())) {
//					return estimatedQty > product.getQuantity() ? true : false;
//				}
//			}
//		}
//		return true;
//	}

	@Logging
	public ResponseDTO<Result<GRNSummaryDTO>> getGRNSummary(String grnCode, String facilityCode) throws Exception {
		GRNMaster grnMaster = grnMasterDAO.getGRNMasterSummary(grnCode, MDC.get("USER_ID"));
		if(grnMaster == null)
			throw new ResponseStatusException(HttpStatus.NOT_FOUND, "No record found for grnCode : " + grnCode);
		GRNSummaryDTO grnSummaryDTO = GRNUtils.getSummary(grnMaster);

		List<GRNPIDMaster> pids = grnPidDao.getGRNPIDDetails(Collections.singletonList(grnMaster.getGrnCode()));
		if(!pids.isEmpty()) {
			List<String> grnPids = pids.stream().map(x -> x.getPid()).collect(Collectors.toList());
			Map<String, Map<String, Object>> pidCounts = grnItemService.getGRNPidDetails(pids);
			Map<String, Map<String, Object>> pidMisc = grnItemDAO.getMisc(grnCode, grnPids);
			Map<String, Timestamp> failedMap = grnItemDAO.getFailedMap(grnCode, grnPids);
			int boxCount = grnItemDAO.getBoxCount(grnCode);
			Map<String, PIDSummaryDTO> pidSummaryDTOMap = GRNUtils.getPIDSummary(pids, pidCounts, grnMaster.getInvoice(), grnSummaryDTO, pidMisc, failedMap);
			grnSummaryDTO.setPids(pidSummaryDTOMap.values().stream().collect(Collectors.toList()));
			grnSummaryDTO.setBoxes(boxCount);
		}

		FacilityDetailsResponse facilityDetailsResponse = facilityDetailsUtils.getFacilityDetails(facilityCode);
		if(facilityDetailsResponse != null && facilityDetailsResponse.getFacilityDetails() != null) {
			grnSummaryDTO.setFromParty(facilityDetailsResponse.getFacilityDetails().getDisplayName());
		}
		ResponseDTO<Result<GRNSummaryDTO>> responseDTO = new ResponseDTO<>();
		Result<GRNSummaryDTO> result = new Result<>();
		result.setData(grnSummaryDTO);
		responseDTO.setResult(result);
		responseDTO.setDisplayMessage(GRN_SUMMARY_SUCCESS);
		return responseDTO;
	}

	public void getRowCounts(String grnCode, Map<String, VendorSTDResponseDTO> map) {
		String[] pids = map.keySet().stream().toArray(String[]::new);
		log.info("GET row counts for pids : " + pids);
		if(pids == null)
			return;
		Map<String, Integer> scanCount = grnItemDAO.getTotalScanned(grnCode, pids);
		if(!scanCount.isEmpty()){
			for(String pid : scanCount.keySet())
				map.get(pid).setTotalScanned(scanCount.get(pid));
		}
		Map<String, Integer> failCount = grnItemDAO.getTotalFailed(grnCode, pids);
		if(!failCount.isEmpty()){
			for(String pid : failCount.keySet())
				map.get(pid).setTotalFailed(failCount.get(pid));
		}
		log.info("Row counts for pids are : " + map);
	}

	public String getUnicomGrnCode(GRNMaster grnMaster) throws JsonProcessingException {
		UnicomCreateGRNDTO unicomCreateGRNDTO = GRNUtils.mapToUnicomGRNDTO(grnMaster);
		UnicomResponse unicomResponse = RetryUtils.postData(grnConfig.getUnicomBaseUrl() +
				grnConfig.getUnicomCreateGrn(), unicomCreateGRNDTO, UnicomResponse.class);
		if(unicomResponse.getSuccessful() == null || !unicomResponse.getSuccessful().booleanValue())
			throw new ApplicationException("Unicom create grn failed : " + unicomResponse.getErrors(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		TypeReference<Map<String, Object>> typeRef = new TypeReference<Map<String, Object>>() {};
		ObjectMapper mapper = new ObjectMapper();
		Map<String, Object> result = null;
		try {
			result = mapper.readValue(mapper.writeValueAsString(unicomResponse.getResult()), typeRef);
		} catch (JsonProcessingException ex) {
			log.error("Exception while parsing vendor standard response", ex);
			throw ex;
		}
		return (String) result.get("unicom_grn_code");
	}

	public List<GRNDetailsDTO> getGRNDetailsByInvoice(String invoiceId) throws Exception {

		log.info("Fetching grn by invoice and user : {}", invoiceId);
		List<GRNMaster> grns = grnMasterDAO.getGRNByInvoiceAndUser(invoiceId, MDC.get("USER_ID"), true);
		if(CollectionUtils.isEmpty(grns)) {
			log.info("No grn found for : {}", invoiceId);
			throw new ResponseStatusException(HttpStatus.NOT_FOUND, "No grn found for this invoice and user");
		}

		List<String> grnCodes = new ArrayList<>();
		for(GRNMaster grnMaster : grns) {
			grnCodes.add(grnMaster.getGrnCode());
		}
		log.info("Fetching grn pid details : {}", invoiceId);
		List<GRNPIDMaster> grnPids = grnPidDao.getGRNPIDDetails(grnCodes);

		log.info("Fetching grn scanned items count : {}", invoiceId);
		Map<String, Map<String, Object>> grnPidCounts = grnItemService.getGRNPidDetails(grnPids);

		log.info("Creating grn pid map : {}", invoiceId);
		Map<String, GRNDetailsDTO> grnPidsMap = GRNUtils.getGRNPidMap(grnPids, grnPidCounts);

		log.info("Fetching invoice map for grn : {}", invoiceId);
		Invoice invoice = grnMasterDAO.getInvoiceById(invoiceId);

		log.info("Preparing grn map for result : {}", invoiceId);
		List<GRNDetailsDTO> grnList = GRNUtils.getGRNList(grns, grnPidsMap, invoice);

		return grnList;

	}

	@Logging
	public List<GRNDetailsDTO> getGRNListByInvoice(String invoiceId) throws JsonMappingException, JsonProcessingException {
		List<GRNDetailsDTO> grns = null;
		if (skipSlowInvoiceIdQuery) {
			grns = grnMasterDAO.getGRNByInvoiceId(invoiceId);
		} else {
			grns = grnMasterDAO.getGRNLISTByInvoiceAndUser(invoiceId, MDC.get("USER_ID"));
		}
		if(CollectionUtils.isEmpty(grns)) {
			return new ArrayList<>();
		}

		List<String> grnCodes = new ArrayList<>();
		for(GRNDetailsDTO grn : grns) {
			grnCodes.add(grn.getGrnCode());
		}

		Map<String,Map<String,Object>> scanCountMap = grnItemDAO.getGRNScanDetails(grnCodes);
		for(GRNDetailsDTO grn : grns) {
			Map<String, Object> grnScanCount = scanCountMap.getOrDefault(grn.getGrnCode(), null);
			int totalScanned = grnScanCount != null ?
					Integer.parseInt(grnScanCount.getOrDefault(TOTAL_SCANNED, "").toString())
					: 0;
			int totalPassed = grnScanCount != null ?
					Integer.parseInt(grnScanCount.getOrDefault(TOTAL_PASSED, "").toString())
					: 0;
			int totalFailed = totalScanned - totalPassed;
			grn.setTotalScanned(totalScanned);
			grn.setTotalFailCount(totalFailed);
		}
		return grns;
	}

	@Logging
	public boolean checkAllGRNClosedForInvoice(String invoiceId, String pid) {

		if(StringUtils.isNotBlank(pid)) {
			Map<String, Boolean> map = grnMasterDAO.checkClosedGRNByInvAndPid(invoiceId, Arrays.asList(pid));
			return !map.get("isGRNOpen");
		}
		int count = grnMasterDAO.getNotClosedGRNCount(invoiceId);
		return count > 0 ? false : true;
	}

	@Logging
	public SearchResponse<List<GRNSearchResponseDTO>> grnSearch(GRNSearchDTO grnSearchReq) {

		Map<String, Object> result = null;
		try {
			List<GRNMasterDetails> grnList = grnMasterDAO.searchGRN(grnSearchReq, false);
			if(CollectionUtils.isEmpty(grnList)) {
				throw new ResponseStatusException(HttpStatus.NO_CONTENT, "No data found");
			}

			List<String> grns = new ArrayList<>();
			for(GRNMaster grn : grnList) {
				grns.add(grn.getGrnCode());
			}

			Map<String, Map<String,Object>> grnScanCountMap = grnItemDAO.getGRNScanDetails(grns);

			List<GRNSearchResponseDTO> grnSearch = GRNUtils.getGRNSearchResponse(grnList, grnScanCountMap);
			int totalCount = grnMasterDAO.countGRNListing(grnSearchReq);
			return new SearchResponse<List<GRNSearchResponseDTO>>(grnConfig.getPageSize(), totalCount, grnSearch);
		} catch (ResponseStatusException ex) {
			throw ex;
		} catch (Exception ex) {
			throw new ResponseStatusException(HttpStatus.EXPECTATION_FAILED, ex.getMessage());
		}
	}

	public void exportGRNDetails(GRNSearchDTO grnSearchReq, HttpServletResponse response) throws IOException {

		log.info("Inside exportGRNDetails method with request : {}", grnSearchReq);
		String filename = "grn-details.csv";
		CSVPrinter csvPrinter = null;
		try {

			List<GRNMasterDetails> grnList = grnMasterDAO.searchGRN(grnSearchReq, true);
			if (CollectionUtils.isEmpty(grnList)) {
				log.info("No grn details found for reqest : {}", grnSearchReq);
				throw new ResponseStatusException(HttpStatus.NO_CONTENT, "No data found");
			}

			List<String> grns = new ArrayList<>();
			for (GRNMaster grn : grnList) {
				grns.add(grn.getGrnCode());
			}

			Map<String, Map<String, Object>> grnScanCountMap = grnItemDAO.getGRNScanDetails(grns);

			List<GRNSearchResponseDTO> grnDetails = GRNUtils.getGRNSearchResponse(grnList, grnScanCountMap);
			response.setContentType("text/csv");
			response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"");
			csvPrinter = new CSVPrinter(response.getWriter(),
					CSVFormat.DEFAULT.withHeader(GRNDetailsCSV.HEADER1, GRNDetailsCSV.HEADER2, GRNDetailsCSV.HEADER3, GRNDetailsCSV.HEADER4,
							GRNDetailsCSV.HEADER5, GRNDetailsCSV.HEADER6, GRNDetailsCSV.HEADER7, GRNDetailsCSV.HEADER8, GRNDetailsCSV.HEADER9));

			for (GRNSearchResponseDTO grn : grnDetails) {
				csvPrinter.printRecord(Arrays.asList(grn.getGrnCode(), grn.getGrnStatus(), grn.getPoId(), grn.getInvoiceRefNum(), grn.getVendor(),
						grn.getCreatedBy(), grn.getCreatedOn(), grn.getTotalQty(), grn.getQcComplete()));
			}

		} catch (IOException ex) {
			log.error(ex.getMessage());
			throw ex;
		} catch (Exception ex) {
			log.error(ex.getMessage());
			throw new ResponseStatusException(HttpStatus.EXPECTATION_FAILED, ex.getMessage());
		} finally {
			if (csvPrinter != null) {
				csvPrinter.close();
			}
		}

	}

	@Logging
	public Map<String, Boolean> isGRNOpen(String invoiceId, List<String> pids) {

		Map<String, Boolean> map = grnMasterDAO.checkClosedGRNByInvAndPid(invoiceId, pids);
		for(String pid : pids) {
			if(StringUtils.isNotBlank(pid) && !map.containsKey(pid)) {
				map.put(pid, false);
			}
		}

		return map;

	}

	public void validateGRN(Invoice invoice, String facilityCode) throws JsonProcessingException, SocketTimeoutException {
		Map<String, String> request = new HashMap<>();
		request.put("invoice_ref_no", invoice.getInvoiceRefNum());
		validate(request, facilityCode);
	}

	private void validatePID(Invoice invoice, PIDEstimatedQty pidEstimatedQty, String facilityCode) throws JsonProcessingException, SocketTimeoutException {
		Product product = new Product(pidEstimatedQty.getPid(), pidEstimatedQty.getEstimatedQuantity());
		PIDValidationDTO pidValidationDTO = new PIDValidationDTO(invoice.getInvoiceRefNum(), Collections.singletonList(product));
		validate(pidValidationDTO, facilityCode);
	}

	public void validate(Object request, String facilityCode) throws JsonProcessingException, SocketTimeoutException {
		log.info("[validate] request {}", request);
		ObjectMapper mapper = new ObjectMapper();
		Map<String, String> headerMap = new HashMap<>();
		headerMap.put("facility-code", facilityCode);
		GRNValidationResponse response = null;
		try {
			response = RetryUtils.postData(grnConfig.getInvoiceBaseUrl() + grnConfig.getGrnValidationUrl(),headerMap,
					request, GRNValidationResponse.class);
		} catch (HttpClientErrorException.BadRequest ex) {
			log.error("[validate] Exception while grn validation : " + ex.getMessage());
			try {
				response = mapper.readValue(ex.getResponseBodyAsString(), GRNValidationResponse.class);
			} catch (JsonProcessingException je) {
				log.error("Exception while grn validation json parsing : " + ex.getMessage());
				throw je;
			}
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, response.getMeta().getDisplayMessage());
		} catch (Exception e ) {
			log.error("[validate] Exception {}, error {}", request, e.getMessage());
			throw e;
		}


		if (response != null && response.getData() != null && response.getData().isValidated())
			return;
		else {
			String displayMessage = response != null && response.getMeta() != null ? response.getMeta().getDisplayMessage() : null;
			throw new ApplicationException(displayMessage, GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

    public InputStream getGRNPdf(String grnCode, String facilityCode) throws Exception {
		GRNMaster grnMaster = grnMasterDAO.getGRNMasterSummary(grnCode, MDC.get("USER_ID"));
		if(grnMaster == null)
			throw new ResponseStatusException(HttpStatus.NOT_FOUND, "No record found for grnCode : " + grnCode);
		GRNSummaryDTO grnSummaryDTO = GRNUtils.getSummary(grnMaster);

		List<GRNPIDMaster> pids = grnPidDao.getGRNPIDDetails(Collections.singletonList(grnMaster.getGrnCode()));
		if(!pids.isEmpty()) {
			List<String> grnPids = pids.stream().map(x -> x.getPid()).collect(Collectors.toList());
			Map<String, Map<String, Object>> pidCounts = grnItemService.getGRNPidDetails(pids);
			Map<String, Map<String, Object>> pidMisc = grnItemDAO.getMisc(grnCode, grnPids);
			Map<String, Timestamp> failedMap = grnItemDAO.getFailedMap(grnCode, grnPids);
			int boxCount = grnItemDAO.getBoxCount(grnCode);
			Map<String, PIDSummaryDTO> pidSummaryDTOMap = GRNUtils.getPIDSummary(pids, pidCounts, grnMaster.getInvoice(), grnSummaryDTO, pidMisc, failedMap);
			grnSummaryDTO.setPids(pidSummaryDTOMap.values().stream().collect(Collectors.toList()));
			grnSummaryDTO.setBoxes(boxCount);
		}

		FacilityDetailsResponse facilityDetailsResponse = facilityDetailsUtils.getFacilityDetails(facilityCode);
		if (newOrOldValidateFacilityCodeCheck.contains(facilityCode)) {
			log.info("[getFacilityName] BEFORE grnCode {}, facilityCode {}", grnCode, facilityCode);
			com.lenskart.nexs.common.entity.po.PurchaseOrder purchaseOrder = purchaseOrderEntityService.getPurchaseOrder(grnMaster.getPoId());
			String newFacName = getFacilityName(purchaseOrder.getCreatedAt());
			facilityDetailsResponse.getFacilityDetails().setLegalName(newFacName);
			facilityDetailsResponse.getFacilityDetails().setDisplayName(newFacName);
			log.info("[getFacilityName] grnCode {}, facilityCode {}, newFacName {}", grnCode, facilityCode, newFacName);
		}

		if(facilityDetailsResponse != null && facilityDetailsResponse.getFacilityDetails() != null) {
			grnSummaryDTO.setFromParty(facilityDetailsResponse.getFacilityDetails().getDisplayName());
		}
		CommonUtils.updateFormat(grnSummaryDTO);
		String template = grnMasterDAO.getTemplate(grnConfig.getGrnViewPdfTemplateId(), grnConfig.getGrnViewPdfTemplateVersion());
		if(template == null)
			throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Template not found");
		return pdfGeneratorService.createPdf(template, grnSummaryDTO);
    }

	public String getFacilityName(Date poCreatedDate) {
		log.info("[getFacilityName] poCreatedDate {}", poCreatedDate);
		// Compare and return appropriate name
		long poCreatedEpoch = poCreatedDate.getTime() / 1000; // Convert milliseconds to seconds
		log.info("[getFacilityName] poCreatedDate {}, poCreatedEpoch {}, showOldFacilityEpochDate {}, " +
						"poCreatedEpoch < showOldFacilityEpochDate = {}, newOrOldValidateFacilityCodeCheck {}",
				poCreatedDate, poCreatedEpoch, showOldFacilityEpochDate,
				poCreatedEpoch < showOldFacilityEpochDate, newOrOldValidateFacilityCodeCheck);

		if (poCreatedEpoch < showOldFacilityEpochDate) {
			log.info("[getFacilityName] Show old facility Name poCreatedEpoch {}, showOldFacilityEpochDate {}, oldFacilityName {}", poCreatedEpoch, showOldFacilityEpochDate, oldFacilityName);
			return oldFacilityName;
		}
		log.info("[getFacilityName] Returning existing facility Name facilityCode {}", newFacilityName);
		return newFacilityName;
	}

    @Logging
	public List<Map<String, String>> getClosedGRNs(String invoiceRefNum, String facility) {
		List<GRNMaster> grnMasterList = grnMasterDAO.getClosedGRNs(invoiceRefNum, facility);
		List<Map<String, String>> list = new ArrayList<>();

		for (GRNMaster grnMaster : grnMasterList) {
			Map<String, String> map = new HashMap<>();
			map.put("n", grnMaster.getGrnCode());
			map.put("u", grnMaster.getUnicomGrnCode());
			list.add(map);
		}
		return list;
	}

	@Logging
	public List<String> getGRNListByPO(String poNum) throws Exception {
		List<String> grns = grnMasterDAO.getGRNByPOAndUser(poNum);
		if(CollectionUtils.isEmpty(grns))
			throw new ResponseStatusException(HttpStatus.NOT_FOUND, "No grn found for po : " + poNum);
		return grns;
	}

	@Logging
	public GRNMaster getGRNMaster(String grnCode) throws Exception {
		GRNMaster grnMaster = grnMasterDAO.getGRNMaster(grnCode, MDC.get("USER_ID"));
		if (grnMaster == null)
			log.error("[getGRNMaster] No record found for grnCode : {}", grnCode);
		return grnMaster;
	}

	@Logging
	public String getGRNListByPOInvoice(String invoiceId) {
		String grnCodes = grnMasterDAO.getGrnByInvoice(invoiceId);
		if(grnCodes.isEmpty())
			throw new ResponseStatusException(HttpStatus.NOT_FOUND, "No grn found for poInvoice : " + invoiceId);
		return grnCodes;
	}

	@Scheduled(cron = "0 */30 * * * *")
	@SchedulerLock(name = "syncGRNsToUnicomScheduledTask")
	public void syncGRNsToUnicom() {
		log.info("[syncGRNsToUnicom] Sync grn to unicom, schedular started");
		List<UnicomUnsyncedGrnCodeDetails> grnCodeToFacilityCodeList = grnMasterDAO.getUnsyncedGrn(Queries.GET_UNSYNCED_GRN);
		log.info("[syncGRNsToUnicom] grnCodeToFacilityCodeList : {}", grnCodeToFacilityCodeList);
		syncUnsyncedData(grnCodeToFacilityCodeList);
		log.info("[syncGRNsToUnicom] Successfully Synced grnCodeToFacilityCodeList : {}", grnCodeToFacilityCodeList);
	}

	@Scheduled(cron = "* * */3 * * ?")
	@SchedulerLock(name = "syncOldUnsyncedGrnsToUnicomScheduledTask")
	public void syncFailedGRNsToUnicom() {
		log.info("[getOldUnsyncedGrn] Sync grn to unicom, schedular started");
		List<UnicomUnsyncedGrnCodeDetails> grnCodeToFacilityCodeList = grnMasterDAO.getUnsyncedGrn(Queries.GET_OLD_UNSYNCED_GRN);
		log.info("[getOldUnsyncedGrn] grnCodeToFacilityCodeList : {}", grnCodeToFacilityCodeList);
		syncUnsyncedData(grnCodeToFacilityCodeList);
		log.info("[getOldUnsyncedGrn] Successfully Synced grnCodeToFacilityCodeList : {}", grnCodeToFacilityCodeList);
	}

	private void syncUnsyncedData(List<UnicomUnsyncedGrnCodeDetails> grnCodeToFacilityCodeList) {
		log.info("[syncGRNsToUnicom] grnCodeToFacilityCodeMap : {}", grnCodeToFacilityCodeList);
		for(UnicomUnsyncedGrnCodeDetails grnCodeToFacilityCode :grnCodeToFacilityCodeList) {
			boolean reconcileData = closeGrnReconcileExcludeCategory == null ||
					(!closeGrnReconcileExcludeCategory.contains(grnCodeToFacilityCode.getGrnType()) &&
							!closeGrnReconcileExcludeCategory.contains("ALL"));

			Map<String, Object> headers = new HashMap<>();
			headers.put("USER_ID", MDC.get("USER_ID"));
			headers.put("USER_MAIL", MDC.get("USER_MAIL"));
			headers.put("FACILITY_CODE", grnCodeToFacilityCode.getFacility());
			headers.put("RECONCILE_DATA", reconcileData);
			log.info("[syncGRNsToUnicom] Producing a message to kafka producer for grn code {} with headers: {}",
					grnCodeToFacilityCode.getGrnCode(), headers);
			RetryUtils.sendMessage(grnCodeToFacilityCode.getGrnCode(), grnConfig.getTopicName(), headers);
			log.info("[syncGRNsToUnicom] Successfully saved details for grn code {} with headers: {}",
					grnCodeToFacilityCode.getGrnCode(), headers);
		}
	}

	public String createTransferGRN(CreateTransferGrnRequest request) throws Exception {
		log.info("Create transfer GRN {}", request);
		CreateGRNMasterDTO createGRNMasterDTO = new CreateGRNMasterDTO();
		Invoice invoice = poInvoiceService.getInvoiceDetailsByVendorInvoiceNum(request.getVendorInvoiceNumber());
		PurchaseOrder purchaseOrder = poInvoiceService.getPurchaseOrderDetails(request.getPoNum());
		createGRNMasterDTO.setPo(purchaseOrder);
		createGRNMasterDTO.setInvoice(invoice);
		createGRNMasterDTO.setFacilityCode(request.getFacilityCode());
		MDC.put("FACILITY_CODE", request.getFacilityCode());
		CreateGRNResponseDTO response = createGRN(createGRNMasterDTO,
				request.getFacilityCode(), GRNConstants.TRANSFER_TYPE);
		log.info("Response to create grn {}", response);
		return response.getGrnCode();
	}

	public String createGRNMaster(CreateGrnMasterRequest request) {
		try{
			log.info("Create GRN Master vendor invoice num {}, request {}",
					request.getVendorInvoiceNumber(), request);
			Invoice invoice = poInvoiceService.getInvoiceDetailsByVendorInvoiceNum(request.getVendorInvoiceNumber());
			PurchaseOrder purchaseOrder = poInvoiceService.getPurchaseOrderDetails(invoice.getPoId());
			CreateGRNMasterDTO createGRNMasterDTO = createGRNMasterDTO(purchaseOrder, invoice,
					request.getFacilityCode(), request.getCreatedBy());
			CreateGRNResponseDTO response = new CreateGRNResponseDTO();
			if(request.getType()!= null && !request.getType().isEmpty()
					&& request.getType().equalsIgnoreCase(GRNConstants.JIT_TYPE)){
				response = createGRN(createGRNMasterDTO, request.getFacilityCode(), GRNConstants.JIT_TYPE);
			} else{
				log.error("Please give correct type to create grn");
				throw new ApplicationException("Please give correct type to create grn",
						GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
			log.info("Response to create grn for orders {}", response);
			return response.getGrnCode();
		} catch (Exception e){
			log.error("Error in creating grn {}, error {}", request, e.getMessage());
			throw new ApplicationException("Exception : " + e.getMessage(),
					GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	private CreateGRNMasterDTO createGRNMasterDTO(PurchaseOrder purchaseOrder, Invoice invoice,
												  String facilityCode, String createdBy) {
		CreateGRNMasterDTO createGRNMasterDTO = new CreateGRNMasterDTO();
		createGRNMasterDTO.setPo(purchaseOrder);
		createGRNMasterDTO.setInvoice(invoice);
		createGRNMasterDTO.setFacilityCode(facilityCode);
		MDC.put("FACILITY_CODE", facilityCode);
		MDC.put("USER_ID", createdBy);
		log.info("[createGRNMasterDTO] createGRNMasterDTO {}", createGRNMasterDTO);
		return createGRNMasterDTO;
	}

	public List<IqcGrnProductEntity> saveGrnProductSamplingDetails(String grnCode) {
		try{
			log.info("[saveGrnProductSamplingDetails] grnCode {}", grnCode);
			List<GrnItemBoxBarcodePidMapping> grnItemBoxBarcodePidMappingList =
					grnItemService.getBoxCodeAndProductId(grnCode);
			List<IqcGrnProductEntity> iqcGrnProductEntities = new ArrayList<>();
			if(grnItemBoxBarcodePidMappingList.isEmpty()){
				return iqcGrnProductEntities;
			}

			List<Integer> productIdList = grnItemBoxBarcodePidMappingList.stream().
					map(GrnItemBoxBarcodePidMapping::getPid).collect(Collectors.toList());
			log.info("[saveGrnProductSamplingDetails] grnCode {}, productIdList {}",
					grnCode, productIdList);

			List<PurchaseInvoiceItemEntity> invoiceItemEntities =
					purchaseInvoiceItemEntityService.findByInvoiceRefNumberAndProductIdIn(
							Integer.parseInt(grnItemBoxBarcodePidMappingList.get(0).getInvoiceRefNum()),
							productIdList);
			Map<Integer, PurchaseInvoiceItemEntity> purchaseInvoiceItemEntityMap = new HashMap<>();
			for(PurchaseInvoiceItemEntity invoiceItemEntity: invoiceItemEntities){
				purchaseInvoiceItemEntityMap.put(invoiceItemEntity.getProductId(), invoiceItemEntity);
			}
			log.info("[saveGrnProductSamplingDetails] grnCode {}, purchaseInvoiceItemEntityMap {}",
					grnCode, purchaseInvoiceItemEntityMap);

			for(GrnItemBoxBarcodePidMapping boxBarcodePidMapping : grnItemBoxBarcodePidMappingList){
				if(purchaseInvoiceItemEntityMap.containsKey(boxBarcodePidMapping.getPid())){
					PurchaseInvoiceItemEntity purchaseInvoiceItemEntity =
							purchaseInvoiceItemEntityMap.get(boxBarcodePidMapping.getPid());
					log.info("[saveGrnProductSamplingDetails] grnCode {}, pid {}, " +
							"total pid count {}, sampling per {}", grnCode,
							boxBarcodePidMapping.getPid(), boxBarcodePidMapping.getTotalPidCount(),
							purchaseInvoiceItemEntity.getIqcSamplingPercent());
					if(purchaseInvoiceItemEntity.getIqcSamplingPercent() != 0){
						Integer totalQty = boxBarcodePidMapping.getTotalPidCount();
						Integer samplingQty = (int) (boxBarcodePidMapping.getTotalPidCount() *
								purchaseInvoiceItemEntity.getIqcSamplingPercent() / 100);
                        log.info("[saveGrnProductSamplingDetails] grnCode {}, pid {}, " +
                                        "total pid count {}, sampling per {}, totalQty {}, samplingQty {}", grnCode,
                                boxBarcodePidMapping.getPid(), boxBarcodePidMapping.getTotalPidCount(),
                                purchaseInvoiceItemEntity.getIqcSamplingPercent(), totalQty, samplingQty);
						if(samplingQty >= 1){
                            ObjectMapper mapper = new ObjectMapper();
                            Map<String, Object> productAttributesMapping =
                                    mapper.readValue(purchaseInvoiceItemEntity.getProductAttributes(),
                                            new TypeReference<Map<String, Object>>() {
                                            });
                            String pidDesc = "";
                            if (productAttributesMapping.get("desc") != null)
                                pidDesc = (String) productAttributesMapping.get("desc");
                            log.info("[saveGrnProductSamplingDetails] grnCode {}, pid {}, boxCode {}, " +
                                            "total pid count {}, sampling per {}, totalQty {}, samplingQty {}, pidDesc {}",
                                    grnCode, boxBarcodePidMapping.getPid(), boxBarcodePidMapping.getQcPassBoxBarcode(),
									boxBarcodePidMapping.getTotalPidCount(),
                                    purchaseInvoiceItemEntity.getIqcSamplingPercent(), totalQty, samplingQty, pidDesc);
							IqcGrnProductEntity iqcGrnProductEntity =
									iqcGrnProductEntityService.findByPidAndGrnCodeAndBoxCode(
											boxBarcodePidMapping.getPid().toString(),
											grnCode, boxBarcodePidMapping.getQcPassBoxBarcode());
							log.info("[saveGrnProductSamplingDetails] iqcGrnProductEntity {}", iqcGrnProductEntity);
							if(iqcGrnProductEntity == null){
								iqcGrnProductEntity = new IqcGrnProductEntity(
										boxBarcodePidMapping.getInvoiceRefNum(),
										grnCode, boxBarcodePidMapping.getQcPassBoxBarcode(),
										boxBarcodePidMapping.getPid().toString(), pidDesc,
										totalQty, samplingQty,
										0, 0, purchaseInvoiceItemEntity.getIqcSamplingPercent(),
										purchaseInvoiceItemEntity.getIqcSamplingPercent(),
										IqcGrnProductStatusEnum.CREATED, 0);
							} else {
								iqcGrnProductEntity.setTotalQty(totalQty);
								iqcGrnProductEntity.setSamplingQty(samplingQty);
							}
							iqcGrnProductEntity.setCreatedAt(new Date());
							iqcGrnProductEntity.setUpdatedAt(new Date());
							iqcGrnProductEntity.setCreatedBy(MDC.get("USER_ID"));
							iqcGrnProductEntity.setUpdatedBy(MDC.get("USER_ID"));
                            iqcGrnProductEntities.add(iqcGrnProductEntity);
						}
					}
				}
			}

			iqcGrnProductEntityService.saveOrUpdateAll(iqcGrnProductEntities);
			log.info("[saveGrnProductSamplingDetails] grnCode {}, iqcGrnProductEntities {}",
					grnCode, iqcGrnProductEntities);

			return iqcGrnProductEntities;
		} catch (Exception e){
			log.error("Unable to store details in grn product sampling {}, {}",grnCode, e.getMessage());
			if(e.getMessage().contains("ConstraintViolationException") &&
					e.getMessage().contains("grn_code_box_code_pid")){
				log.error("Details already exist in iqc grn product table {}", grnCode);
				return new ArrayList<IqcGrnProductEntity>();
			} else {
				throw new ApplicationException("Exception : " + e.getMessage(),
					GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
		}
	}

	private void saveToD365Table(String grnCode) {
		GRND365Sync grnD365Sync = new GRND365Sync();
		grnD365Sync.setGrnCode(grnCode);
		grnD365Sync.setCount(-1);
		String userId = MDC.get("USER_ID");
		if (StringUtils.isEmpty(userId))
			userId = "API";
		grnD365Sync.setCreatedBy(userId);
		grnD365Sync.setUpdatedBy(userId);
		grnD365Sync.setHistoryId(0);
		grnD365Sync.setRetryable(true);
		log.info("[saveToD365Table] calling finance-adaptor for grn code :{}", grnCode);
		grnD365SyncEntityService.saveOrUpdate(grnD365Sync);
		if (enableFinanceConsumerFlow) {
			publishGrnToFinanceService(grnD365Sync);
		}
		if (enableSyncToPlatform) {
			financePlatformService.publishGrnToFinancePlatform(grnD365Sync);
		}
		log.info("Grn Saved to d365 : {}", grnCode);
	}

	private void publishGrnToFinanceService(GRND365Sync grnD365Sync) {
		GrnMasterEntity grnMaster = grnMasterEntityService.findByGrnCode(grnD365Sync.getGrnCode());
		FacilityDetailsResponse facilityDetailsResponse =facilityDetailsUtils.getFacilityDetails(grnMaster.getFacility());
		if(Objects.nonNull(facilityDetailsResponse)) {
			log.info("[publishGrnToFinanceService] grnD365Sync :{}", grnD365Sync);
			String url = financeConsumerUrl + grnEventCreateUrl;
			GrnEventCreationRequest grnEventCreationRequest = GrnEventCreationRequest.builder()
					.eventName(BaseEventCreationRequest.FinanceServiceEventTypes.GRN)
					.source(com.lenskart.nexs.grn.enums.Source.NEXS)
					.version(GrnEventCreationRequest.Version.V1)
					.eventId(grnD365Sync.getGrnCode())
					.legalEntity(facilityDetailsResponse.getFacilityDetails().getLegalOwner())
					.facilityCode(grnMaster.getFacility())
					.purchaseOrderNumber(grnMaster.getPoId())
					.build();
			log.info("[publishGrnToFinanceService] grnEventCreationRequest :{}", grnEventCreationRequest);
			grnD365Sync.setSyncedToFinance(financeConsumerConnector.sendGrnEventToFinanceService(url,
																								 HttpMethod.POST,
																								 grnEventCreationRequest));
		}
	}

	public AutoGrnScanDetails createGrnDetails(AutoGrnScanDetails request)  throws Exception{
		try {
			log.info("[createGrnDetails] createGrnDetails : {}, max count {}", request, grnConfig.getBarcodeMaxSize());
			if (request.getGrnItemDetailList() != null && request.getGrnItemDetailList().size() > grnConfig.getBarcodeMaxSize())
				throw new ApplicationException("Barcode item count is greater than "+ grnConfig.getBarcodeMaxSize(),
						GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);

			String grnCode = request.getGrnCode();
			String poNum = request.getPoNum();
			log.info("[createGrnDetails] createGrnDetails : {}, poNum {}, grnCode {}", request, poNum, grnCode);
			List<PurchaseInvoiceEntity> invoiceEntity = purchaseInvoiceEntityService.findByPoNum(poNum);
			if (invoiceEntity.isEmpty()) {
				throw new ApplicationException("Invoice does not exist for this PO", GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			}
			PurchaseInvoiceEntity purchaseInvoiceEntity = invoiceEntity.get(0);
			request.setCreatedBy(request.getCreatedBy() != null ? request.getCreatedBy() : "SYSTEM");
			validateGrnQty(purchaseInvoiceEntity.getTotalInvoiceQty(), request.getGrnItemDetailList().size());

			if (request.getGrnCode() == null || request.getGrnCode().isEmpty()) {
				List<GrnMasterEntity> grnMasterEntities = grnMasterEntityService.
						findByInvoiceId(String.valueOf(purchaseInvoiceEntity.getInvoiceRefNumber()));
				log.info("[createGrnDetails] invoice ref num {}, grnMasterEntities : {}",
						purchaseInvoiceEntity.getInvoiceRefNumber(), grnMasterEntities);
				if (grnMasterEntities == null || grnMasterEntities.isEmpty()) {
					log.info("[createGrnDetails] Create Grn master : {}", request);
					CreateGRNResponseDTO createGRNResponseDTO = createGrnDetailsGRNMaster(invoiceEntity);
					grnCode = createGRNResponseDTO.getGrnCode();
				} else {
					grnCode = grnMasterEntities.get(0).getGrnCode();
					List<GrnItemEntity> grnItems = grnItemEntityService.findByGrnCode(grnCode);
					if (grnItems.size() == request.getGrnItemDetailList().size()) {
						log.info("[createGrnDetails] Already created for grnCode : {}", grnCode);
						request.setGrnCode(grnCode);
						request.getGrnItemDetailList().forEach(autoGrnItemScanDetail -> {
							if (autoGrnItemScanDetail != null) {
								autoGrnItemScanDetail.setSuccess(true);
								autoGrnItemScanDetail.setError(null);
							}
						});
						return request;
					}
				}
				request.setGrnCode(grnCode);
				log.info("[createGrnDetails] Grn master created : {}", grnCode);
			} else {
				log.info("[createGrnDetails] Validate existing grn status {}", request.getGrnCode());
				grnItemService.checkGrnStatus(request.getGrnCode(), request.getCreatedBy());
			}

			log.info("[createGrnDetails] Create Grn Items : poNum {}, grnCode {}, {}", poNum, grnCode, request);
			createGrnItems(request, purchaseInvoiceEntity);
			log.info("[createGrnDetails] All operations successfully performed for grnCode : {}", grnCode);
			return request;
		} catch (Exception e){
			log.error("[createGrnDetails] createGrnDetails : {}, invoiceNum {},  error {}", request, request.getVendorInvoiceNumber(), e.getMessage());
			throw new ApplicationException("Unable to create GRN "+ e.getMessage(),
					GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	private void validateGrnQty(int totalInvoiceQty, int grnItemSize) {
		log.error("[validateGrnQty] totalInvoiceQty {}, grnItemSize {}", totalInvoiceQty, grnItemSize);
		if(grnItemSize > totalInvoiceQty){
			log.error("[validateGrnQty] Grn Qty cant be more than invoice qty");
			throw new ApplicationException("Grn Qty cant be more than invoice qty", GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	private CreateGRNResponseDTO createGrnDetailsGRNMaster(
			List<PurchaseInvoiceEntity> invoiceEntity) throws Exception {
		log.info("[createGrnDetailsGRNMaster] poNum {}, vendorInvNum {}", invoiceEntity.get(0).getPoNum(),
				invoiceEntity.get(0).getVendorInvoiceNumber());
		PurchaseOrder purchaseOrderDetails = poInvoiceService.getPurchaseOrderDetails(invoiceEntity.get(0).getPoNum());
		Invoice invoice = poInvoiceService.getInvoiceDetailsByVendorInvoiceNum(invoiceEntity.get(0).getVendorInvoiceNumber());
		CreateGRNMasterDTO createGRNMasterDTO = createGRNMasterDTO(purchaseOrderDetails, invoice,
				invoiceEntity.get(0).getFacilityCode(), invoiceEntity.get(0).getCreatedBy());
		com.lenskart.nexs.common.entity.po.PurchaseOrder purchaseOrder = poInvoiceService
				.getPurchaseOrder(invoiceEntity.get(0).getPoNum());
		CreateGRNResponseDTO response = createGRN(createGRNMasterDTO, invoiceEntity.get(0).getFacilityCode(),
				purchaseOrder.getPoType());
		log.info("[createGrnDetailsGRNMaster] poNum {}, vendorInvNum {}, response {}",
				purchaseOrderDetails.getPoId(), invoiceEntity.get(0).getVendorInvoiceNumber(), response);
		return response;
	}

	private void createGrnItems(AutoGrnScanDetails request, PurchaseInvoiceEntity purchaseInvoiceEntity) throws ApplicationException {
		try{
			log.info("[createGrnItems] createGrnDetails : {}, purchaseInvoiceEntity {}", request, purchaseInvoiceEntity);
			grnItemService.createGrnItems(request, purchaseInvoiceEntity);
			log.info("[createGrnItems] Grn item successfully created createGrnDetails : {}", request);
		} catch (Exception e){
			log.error("[createGrnItems] createGrnDetails : {}, error {}", request, e.getMessage());
			throw new ApplicationException(e.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	public void autoGrnCallPushToEMS(AutoGrnScanDetails autoGrnScanDetails) {
		try{
			log.error("[autoGrnCallPushToEMS] autoGrnScanDetails : {}", autoGrnScanDetails.toString());
			pushMessageToEmsKafka(autoGrnScanDetails);
			log.error("[autoGrnCallPushToEMS] Successfully pushed autoGrnScanDetails : {}", autoGrnScanDetails.toString());
		}catch (Exception e){
			log.error("[createGrnItems] autoGrnScanDetails : {}, error {}", autoGrnScanDetails, e.getMessage());
			throw new ApplicationException("Unable to push message to EMS: "+e.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	private void pushMessageToEmsKafka(AutoGrnScanDetails autoGrnScanDetails) {
		try {
			log.info("[pushMessageToEmsKafka] Push message to kafka {}", autoGrnScanDetails);
			Map<String, Object> headers = new HashMap<>();
			ObjectMapper objectMapper=new ObjectMapper();
			EmsExceptionEvent emsExceptionEvent = new EmsExceptionEvent();
			emsExceptionEvent.setCreatedAt(new Date());
			emsExceptionEvent.setSource(Source.GRN);
			emsExceptionEvent.setExceptionType(getExceptionType(autoGrnScanDetails));
			emsExceptionEvent.setMeta(objectMapper.writeValueAsString(autoGrnScanDetails));
			headers.put("USER_ID", "SYSTEM");
			headers.put("USER_MAIL", "SYSTEM");
			headers.put("FACILITY_CODE", autoGrnScanDetails.getFacilityCode());
			log.info("[pushMessageToEmsKafka]  Producing a message to ems kafka producer for barcode {} , message {}, with headers: {}",
					autoGrnScanDetails, emsExceptionEvent, headers);
			RetryUtils.sendMessageWithPartitionKey(emsExceptionEvent,
					emsKafkaTopic, headers, "AUTO_GRN_BARCODE_SCANNED");
			log.info("[pushMessageToEmsKafka] Message successfully pushed to ems {}", autoGrnScanDetails);
		} catch (Exception e){
			log.error("Unable to push message to ems {}: error {}",
					autoGrnScanDetails, e.getMessage());
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, e.getMessage());
		}
	}

	private ExceptionType getExceptionType(AutoGrnScanDetails autoGrnScanDetails) {
		return ExceptionType.AUTO_GRN_BARCODE_SCANNED;
	}

	public AutoGrnScanDetails createNCloseAutoGrn(AutoGrnScanDetails autoGrnScanDetails) {
		try {
			MDC.put("USER_ID", autoGrnScanDetails.getCreatedBy());
			if (autoGrnScanDetails.getCreatePutaway() == null) {
				autoGrnScanDetails.setCreatePutaway(false);
			}
			try {
				List<String> grnListByPO = getGRNListByPO(autoGrnScanDetails.getPoNum());
				autoGrnScanDetails.setGrnCode(grnListByPO.get(0));
				return autoGrnScanDetails;
			} catch (Exception e) {
				log.error("[createNCloseAutoGrn] Unable to createNCloseAutoGrn {}: error {}", autoGrnScanDetails, e.getMessage());
			}
			createGrnDetails(autoGrnScanDetails);
			GRNMaster grnMaster = getGRNMaster(autoGrnScanDetails.getGrnCode());
			log.info("[createNCloseAutoGrn] createGrnDetails is done for grn {} : {}", 
					autoGrnScanDetails.getGrnCode(), grnMaster);
			this.closeGRN(autoGrnScanDetails.getGrnCode(), autoGrnScanDetails.getFacilityCode(), grnMaster, autoGrnScanDetails.getCreatePutaway());
			log.info("[createNCloseAutoGrn] closeGRN is done for grn {} : {}",
					autoGrnScanDetails.getGrnCode(), grnMaster);
			return autoGrnScanDetails;
		} catch (Exception e){
			log.error("[createNCloseAutoGrn] Unable to createNCloseAutoGrn {}: error {}", autoGrnScanDetails, e.getMessage());
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, e.getMessage());
		}
	}

	public void validatePOType(String poNum, String data, String poType) throws CustomException {
		log.info("[validatePOType] PoNum {}, barcode {}, type {}, config {}", poNum, data, poType, grnConfig.getPoTypeUIInwardNotAllowedPoType());
		if (grnConfig.isPoTypeCheckEnable() && grnConfig.getPoTypeUIInwardNotAllowedPoType().contains(poType)) {
            log.error("[validatePOType] {} PO detected: {}. Please scan grn barcode {} using the {} flow.", poType, poNum, data, poType);
			throw new CustomException("This is a " + poType + " PO " + poNum + ". Please use the " + poType + " flow.", 400);
		}
	}
}
