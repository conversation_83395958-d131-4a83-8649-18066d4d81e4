package com.lenskart.nexs.grn.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.common.entity.entityService.invoice.PurchaseInvoiceItemEntityService;
import com.lenskart.nexs.common.entity.entityServiceImpl.invoice.PurchaseInvoiceEntityServiceImpl;
import com.lenskart.nexs.common.entity.po.invoice.PurchaseInvoiceEntity;
import com.lenskart.nexs.common.entity.po.invoice.PurchaseInvoiceItemEntity;
import com.lenskart.nexs.common.entity.repositories.grn.GrnItemRepository;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.constants.QualifierConstants;
import com.lenskart.nexs.grn.dao.*;
import com.lenskart.nexs.grn.dto.request.GRNPidSearchDTO;
import com.lenskart.nexs.grn.dto.request.GetPIDDTO;
import com.lenskart.nexs.grn.dto.request.ManualOverrideReqDTO;
import com.lenskart.nexs.grn.dto.request.VendorSTDInfoDTO;
import com.lenskart.nexs.grn.dto.response.*;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.*;
import com.lenskart.nexs.grn.util.GRNUtils;
import com.lenskart.nexs.grn.util.RetryUtils;
import com.lenskart.nexs.po.common.serviceutil.config.LegalOwnerBatchConfig;
import com.nexs.po.common.enums.InvoiceLevelEnum;
import org.apache.logging.log4j.Logger;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.server.ResponseStatusException;

import javax.persistence.Tuple;
import java.math.BigInteger;
import java.util.*;

@Component
public class GRNPIDMasterService implements GRNConstants {

	@Autowired
	@Qualifier(QualifierConstants.JPA_GRN_PID_DAO)
	private GRNPIDDAO grnpiddao;

	@Autowired
	@Qualifier(QualifierConstants.JPA_GRN_MASTER_DAO)
	private GRNMasterDAO grnMasterDAO;

	@Autowired
	@Qualifier(QualifierConstants.JPA_QC_STATUS_DAO)
	private QcStatusDAO qcStatusDAO;

	@Autowired
	private CacheDAO cacheDAO;

	@Autowired
	private GRNConfig grnConfig;

	@Autowired
	@Qualifier(QualifierConstants.JPA_GRN_ITEM_DAO)
	private GRNItemDAO grnItemDAO;

	@Autowired
	@Qualifier(QualifierConstants.JPA_GRN_QC_LOG_DAO)
	private GRNQcLogDAO grnQcLogDAO;

	@CustomLogger
	private Logger log;

	@Autowired
	PurchaseInvoiceItemEntityService purchaseInvoiceItemEntityService;

	@Value("#{'${box.barcode.disable.legal.owner.list}'.split(',')}")
	private Set<String> boxBarcodeDisableLegalOwnerList;

	@Autowired
	private PurchaseInvoiceEntityServiceImpl purchaseInvoiceEntityService;

	@Autowired
	private GrnItemRepository grnItemRepository;

	@Autowired
	private LegalOwnerBatchConfig legalOwnerBatchConfig;

	@Logging
	public void setManualOverride(String grnCode, String pid, Boolean isEnabled) throws Exception {

		GRNMaster grnMaster = grnMasterDAO.getGRNMaster(grnCode);

		if(grnMaster != null && MDC.get("FACILITY_CODE") != null && grnMaster.getFacility() != null && !grnMaster.getFacility().equals(MDC.get("FACILITY_CODE"))) {
			throw new Exception("Incorrect facility chosen in header");
		}

		if (GRN_STATUS_CLOSED.equalsIgnoreCase(grnMaster.getGrnStatus())) {
			throw new ResponseStatusException(HttpStatus.EXPECTATION_FAILED, "GRN is in closed state, operation is invalid");
		}

		boolean result = grnpiddao.setManualOverride(grnCode, pid, isEnabled);
		if (!result) {
			throw new ResponseStatusException(HttpStatus.EXPECTATION_FAILED, "No data found for given grn code and pid");
		}

		if(isEnabled) {
			qcStatusDAO.manualOverrideGRN(grnCode, pid);
		}

	}

	@Logging
	public PIDResponseDTO getPID(GetPIDDTO getPIDDTO) {
		String pid = null;
//		QcConfig qcConfig = null;
		for (Product product : getPIDDTO.getMeta().getInvoice().getPids()) {
			if (getPIDDTO.getScannedId().equals(product.getPid()) || getPIDDTO.getScannedId().equals(product.getGtin())
					|| getPIDDTO.getScannedId().equals(product.getUpc()) || getPIDDTO.getScannedId().equals(product.getVendorSku())) {
				pid = product.getPid();
//				if (RedisHandler.hasKey(CacheUtils.getItemConfigKey(grnConfig.getKeyPrefix(),
//						getPIDDTO.getMeta().getInvoice().getInvoiceRefNum(), pid)))
//					qcConfig = cacheDAO.getInvoiceQcConfig(getPIDDTO.getMeta().getInvoice().getInvoiceRefNum(), pid, getPIDDTO.getGrnCode());
//				else {
//					GRNItem grnItem = new GRNItem();
//					grnItem.setCategoryId(product.getCategoryId());
//					grnItem.setBrand(product.getBrand());
//					grnItem.setVendorCode(getPIDDTO.getMeta().getInvoice().getVendor());
//					grnItem.setPid(pid);
//					grnItem.setInvoiceQuantity(product.getQuantity());
//					qcConfig = cacheDAO.getConfig(grnItem, true);
//				}
//				if (qcConfig == null)
//					throw new ResponseStatusException(HttpStatus.NOT_FOUND, "No qc-config found for given pid");
//				break;
			}
		}

		if(InvoiceLevelEnum.SUMMARY.name().equalsIgnoreCase(getPIDDTO.getInvoiceLevel())) {
			for (POProduct product : getPIDDTO.getMeta().getPo().getPids()) {
				if (getPIDDTO.getScannedId().equals(product.getPid())) {
					pid = product.getPid();
				}
			}
		}

		if (pid == null)
			throw new ResponseStatusException(HttpStatus.NOT_FOUND, "No pid mapping found for scanned_id : " + getPIDDTO.getScannedId());
		PIDResponseDTO pidResponseDTO = new PIDResponseDTO();
		pidResponseDTO.setPid(pid);
//		pidResponseDTO.setSamplingPercent(qcConfig.getSamplingPercent());
		pidResponseDTO.setMeta(getPIDDTO.getMeta());
		return pidResponseDTO;
	}

//	@Logging
//	public SamplingQuantityResponseDTO getSamplingQuantity(CreateGRNPIDTO createGRNPIDTO) {
//		if (isEstimatedQtyMoreThanInvoiceQty(createGRNPIDTO.getMeta().getInvoice(), createGRNPIDTO.getPid().getPid(),
//				createGRNPIDTO.getPid().getEstimatedQuantity()))
//			throw new ResponseStatusException(HttpStatus.EXPECTATION_FAILED, "Estimated quantity is greater than total invoice quantity");
//
//		QcConfig qcConfig = null;
////		if (RedisHandler.hasKey(CacheUtils.getItemConfigKey(grnConfig.getKeyPrefix(), createGRNPIDTO.getMeta().getInvoice().getInvoiceRefNum(),
////				createGRNPIDTO.getPid().getPid())))
////			qcConfig = cacheDAO.getInvoiceQcConfig(createGRNPIDTO.getMeta().getInvoice().getInvoiceRefNum(), createGRNPIDTO.getPid().getPid(), createGRNPIDTO.getGrnCode());
////		else {
////			GRNItem grnItem = GRNItemServiceUtils.getGRNItem(createGRNPIDTO.getMeta(), createGRNPIDTO.getPid().getPid());
////			if (grnItem == null)
////				throw new ApplicationException("Not Valid PID, send correct pid", GRNExceptionStatus.GRN_NOT_FOUND);
////			qcConfig = cacheDAO.getConfig(grnItem, true);
////		}
////		if (qcConfig == null)
////			throw new ResponseStatusException(HttpStatus.NOT_FOUND, "No qc-config found for given pid");
//		long samplingQuantity = (long)Math.ceil(createGRNPIDTO.getPid().getEstimatedQuantity() * qcConfig.getSamplingPercent() / 100.0);
//		SamplingQuantityResponseDTO samplingQuantityResponseDTO = new SamplingQuantityResponseDTO();
//		samplingQuantityResponseDTO.setSamplingQuantity(samplingQuantity);
//		samplingQuantityResponseDTO.setMeta(createGRNPIDTO.getMeta());
//		return samplingQuantityResponseDTO;
//	}

	@Logging
	public BoxItemsResponseDTO getBoxWithItems(String grnCode, String pid, boolean isBoxBarcodeRequired) {
		Map<String, List<Map<String, Object>>> passBoxes = grnItemDAO.getQcPassBoxWithItems(grnCode, pid, isBoxBarcodeRequired);
		if(passBoxes.containsKey(null)){
            passBoxes.put("NO_BOX", passBoxes.remove(null));
        }
        else if(passBoxes.isEmpty()) {
			passBoxes = grnItemDAO.getQcPassItems(grnCode, pid);
		}
		List<Map<String, Object>> failBoxes = grnItemDAO.getQcFailBoxWithItems(grnCode, pid);

		List<Map<String, Object>> others = grnQcLogDAO.getGRNQcLogByGRNAndPid(grnCode, pid);
		BoxItemsResponseDTO boxItemsResponseDTO = new BoxItemsResponseDTO();
		boxItemsResponseDTO.setQcPassed(passBoxes);
		boxItemsResponseDTO.setQcFailed(failBoxes);
		boxItemsResponseDTO.setOthers(others);
		return boxItemsResponseDTO;
	}

	@Logging
	public List<GRNPIDMaster> getGRNPidDetails(List<String> grnCodes) throws Exception {

		return grnpiddao.getGRNPIDDetails(grnCodes);
	}

	@Logging
	public GRNPidDetailsDTO getGRNPidDetails(String grnCode, String pid) throws Exception {

		GRNPIDMaster grnPid = grnpiddao.getGRNPIDMaster(grnCode, pid);

		if (grnPid == null) {
			throw new ResponseStatusException(HttpStatus.NOT_FOUND, "No data exist against given GRN and pid");
		}
		return this.getGRNPidDetails(grnPid);

	}

	@Logging
	public GRNPidDetailsDTO getGRNPidDetails(GRNPIDMaster grnPid) throws Exception {

		if (grnPid == null) {
			throw new ResponseStatusException(HttpStatus.NOT_FOUND, "No data exist against given GRN and pid");
		}

		Map<String, Object> grnPidCounts = grnItemDAO.getGRNItemScannedCount(grnPid.getGrnCode(), Arrays.asList(grnPid.getPid()));
		VendorSTDInfoDTO request = new VendorSTDInfoDTO();
		request.setBrand(grnPid.getBrand());
		request.setCategoryId(grnPid.getCategoryId());
//		request.setEstimatedQty(grnPid.getEstimatedQuantity());
		request.setPid(grnPid.getPid());
		request.setVendor(grnPid.getVendor());

		Map<String, VendorSTDResponseDTO> vendorConfig = this.getVendorSTDConfig(Arrays.asList(request));
		GRNMaster grnMaster = grnMaster = grnMasterDAO.getGRNMaster(grnPid.getGrnCode(), MDC.get("USER_ID"));
		QcConfig qcConfig = null;
		if(grnPidCounts.isEmpty()) {
			for(Product product : grnMaster.getInvoice().getPids()) {
				if(grnPid.getPid().equals(product.getPid())) {
//					if (RedisHandler.hasKey(CacheUtils.getItemConfigKey(grnConfig.getKeyPrefix(),
//							grnPid.getInvoiceReferenceNum(), grnPid.getPid()))) {
//						qcConfig = cacheDAO.getInvoiceQcConfig(grnPid.getInvoiceReferenceNum(), grnPid.getPid(), grnPid.getGrnCode());
//					} else {
//						GRNItem grnItem = new GRNItem();
//						grnItem.setCategoryId(product.getCategoryId());
//						grnItem.setBrand(product.getBrand());
//						grnItem.setVendorCode(grnMaster.getInvoice().getVendor());
//						grnItem.setPid(product.getPid());
//						grnItem.setInvoiceQuantity(product.getQuantity());
//						qcConfig = cacheDAO.getConfig(grnItem, true);
//					}
//					if (qcConfig == null)
//						throw new ResponseStatusException(HttpStatus.NOT_FOUND, "No qc-config found for given pid");
//					break;
				}
			}
		}

		GRNPidDetailsDTO grnPidInfo = GRNUtils.getGRNPIDInfo(grnPid, qcConfig, grnPidCounts, vendorConfig);
		log.info("Fetching sampling detail grn code {}, pid {}, invoice ref num {}",
				grnPidInfo.getGrnCode(), grnPidInfo.getPid(), grnPidInfo.getInvoiceRefNumber());
		PurchaseInvoiceItemEntity invoiceItem = purchaseInvoiceItemEntityService.
				findByInvoiceRefNumberAndProductId(Integer.parseInt(grnPidInfo.getInvoiceRefNumber()),
						Integer.parseInt(grnPidInfo.getPid()));
		if(invoiceItem!= null && invoiceItem.getIqcSamplingPercent() != null)
			grnPidInfo.setSamplingPercent(invoiceItem.getIqcSamplingPercent());

		PurchaseInvoiceEntity invoiceEntity = purchaseInvoiceEntityService.getInvoiceByReferenceNumber(
				Integer.valueOf(grnPidInfo.getInvoiceRefNumber()), true);
		log.info("[getGRNPidDetails] boxBarcodeDisableLegalOwnerList {}, invoiceEntity {}", boxBarcodeDisableLegalOwnerList, invoiceEntity);
		if(boxBarcodeDisableLegalOwnerList.contains(invoiceEntity.getLegalOwner())){
			log.info("[addGrnPID] Setting box barcode required as false");
			grnPidInfo.setIsBoxRequired(false);
		}
		grnPidInfo.setIsLotNoRequired(checkIfLotNoRequired(grnPid,invoiceEntity.getLegalOwner()));
		log.info("[getGRNPidDetails] LotNoRequired is {} {} ", grnPid.getGrnCode(), grnPidInfo.getIsLotNoRequired());
		grnPidInfo.setCurrency(grnMaster.getInvoice() != null ? grnMaster.getInvoice().getCurrency() : null);
		log.info("[getGRNPidDetails] grnPidInfo {}", grnPidInfo);
		return grnPidInfo;
	}

	private boolean checkIfLotNoRequired(GRNPIDMaster grnPid, String legalOwner) {
		log.info("[getGRNPidDetails] checking if lot not is required for grnCode or not {} {} ", grnPid.getGrnCode(), grnPid.getPid());
		boolean isLotNoRequired = false;
		if (legalOwner != null && legalOwnerBatchConfig != null && legalOwnerBatchConfig.getMapping().containsKey(legalOwner)) {
			log.info("[getGRNPidDetails] LegalOwner config mapping and its contains legalOwner or not for grnCode is {} : {} : {} ", grnPid.getGrnCode(), legalOwnerBatchConfig.getMapping(), legalOwnerBatchConfig.getMapping().containsKey(legalOwner));
			isLotNoRequired = legalOwnerBatchConfig.getMapping().get(legalOwner).contains(grnPid.getCategoryId());
			log.info("[getGRNPidDetails] Setting batchNum config is for grncode is {} : {}", grnPid.getGrnCode(), isLotNoRequired);
		}
		return isLotNoRequired;
	}

	private Map<String, VendorSTDResponseDTO> getVendorSTDConfig(List<VendorSTDInfoDTO> request) throws JsonProcessingException {

		TypeReference<Map<String, VendorSTDResponseDTO>> typeRef = new TypeReference<Map<String, VendorSTDResponseDTO>>() {
		};
		ResponseDTO<Map<String, VendorSTDResponseDTO>> responseDTO = RetryUtils.postData(grnConfig.getBaseUrl() + grnConfig.getVendorStandardInfoUrl(),
				request, ResponseDTO.class);
		if (responseDTO == null) {
			log.error("Vendor STD API responded with null");
			throw new ApplicationException("Internal API Error", GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
		ObjectMapper mapper = new ObjectMapper();
		Map<String, VendorSTDResponseDTO> result = null;
		try {
			result = mapper.readValue(mapper.writeValueAsString(responseDTO.getResult()), typeRef);
		} catch (JsonProcessingException ex) {
			log.error("Exception while parsing vendor standard response : ", ex);
			throw ex;
		}
		return result;
	}

//	@Logging
//	public boolean isEstimatedQtyMoreThanInvoiceQty(Invoice invoice, String pid, Long estimatedQty) {
//
//		List<Product> pids = invoice != null ? invoice.getPids() : new ArrayList<>();
//		if (pids != null) {
//			for (Product product : pids) {
//				if (pid.equalsIgnoreCase(product.getPid())) {
//					return estimatedQty > product.getQuantity() ? true : false;
//				}
//			}
//		}
//		return true;
//	}

	@Logging
	public SearchResponse<List<PidSearchResponse>> grnPidSearch(GRNPidSearchDTO grnPidSearchReq) {

		List<PidSearchResponse> result = grnpiddao.grnPidSearch(grnPidSearchReq);
		if(CollectionUtils.isEmpty(result)) {
			throw new ResponseStatusException(HttpStatus.NO_CONTENT, "No data found");
		}
		int totalCount = grnpiddao.countPids(grnPidSearchReq, true);
		return new SearchResponse<List<PidSearchResponse>>(grnConfig.getPageSize(), totalCount, result);
	}

	@Logging
	public List<PidSearchResponse> updateManualOverride(ManualOverrideReqDTO request, GRNPidSearchDTO grnPidSearchReq) {

		if(!CollectionUtils.isEmpty(request.getManualOverrideAllowed())) {
			boolean isUpdated = grnpiddao.updateManualOverrideFlag(request.getManualOverrideAllowed(), true);
			if(isUpdated) {
				qcStatusDAO.setManualOverrideForList(request.getManualOverrideAllowed());
			}
		}

		if(!CollectionUtils.isEmpty(request.getManualOverrideRejected())) {

			grnpiddao.updateManualOverrideFlag(request.getManualOverrideRejected(), false);
		}

		List<PidSearchResponse> result = grnpiddao.grnPidSearch(grnPidSearchReq);
		if(CollectionUtils.isEmpty(result)) {
			throw new ResponseStatusException(HttpStatus.NO_CONTENT, "No data found");
		}
		return result;
	}

	@Logging
	public SearchResponse<GRNProductResponse> getGRNProducts(String grnCode, String facility, int page, int pageSize) {
		GRNProductResponse response = grnpiddao.getGRNProducts(grnCode, page, pageSize);
		if (response == null)
			throw new ResponseStatusException(HttpStatus.NO_CONTENT, "No products found for ");
		response = GRNUtils.getGRNProducts(response);
		GRNPidSearchDTO grnPidSearchReq =  new GRNPidSearchDTO();
		grnPidSearchReq.setFacility(facility);
		grnPidSearchReq.setGrnCode(grnCode);
		int totalCount = grnpiddao.countPids(grnPidSearchReq, false);

		log.info("[getGRNProducts] grnCode {}, facility {}, grn_status {}", grnCode, facility, response.getGrnStatus());
		fetchPutawayCodeList(grnCode, facility, response);
		return new SearchResponse<GRNProductResponse>(pageSize,totalCount,response);
	}

	private void fetchPutawayCodeList(String grnCode, String facility, GRNProductResponse response) {
		try {
			if ("closed".equalsIgnoreCase(response.getGrnStatus())) {
				List<Tuple> putawayCodeTupleList = grnItemRepository.findDistinctPutawayCodeForGRNNum(grnCode);
				List<String> putawayCodeList = new ArrayList<>();
				for (Tuple tuple : putawayCodeTupleList) {
					putawayCodeList.add(String.valueOf((BigInteger) tuple.get("putaway_code")));
				}
				log.info("[getGRNProducts] Setting putaway code  grnCode {}, facility {}, putawayCodeList {}", grnCode, facility, putawayCodeList);
				response.setPutawayList(putawayCodeList);
			}
		} catch (Exception e){
			log.error("[getGRNProducts] grnCode {}, facility {}, grn_status {}, error {}",
					grnCode, facility, response.getGrnStatus(), e.getMessage());
		}
	}

	@Logging
    public SearchResponse<GRNBlockedProduct> grnBlockedPidSearch(GRNPidSearchDTO grnPidSearchReq) {
		List<PidSearchResponse> result = grnpiddao.grnPidSearch(grnPidSearchReq);
		if(CollectionUtils.isEmpty(result))
			throw new ResponseStatusException(HttpStatus.NO_CONTENT, "No data found");
		GRNBlockedProduct grnBlockedProduct = GRNUtils.getBlockedProductResponse(result);
		int totalCount = grnpiddao.countPids(grnPidSearchReq, true);
		return new SearchResponse<GRNBlockedProduct>(grnConfig.getPageSize(),totalCount, grnBlockedProduct);
    }

}
