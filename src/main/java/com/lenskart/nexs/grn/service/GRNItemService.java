package com.lenskart.nexs.grn.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.inventoryadapter.client.InventoryAdapterClient;
import com.lenskart.inventoryadapter.request.CustomRequest;
import com.lenskart.inventoryadapter.response.CustomResponse;
import com.lenskart.nexs.common.entity.entityService.PurchaseOrderItemEntityService;
import com.lenskart.nexs.common.entity.entityService.grn.GrnItemEntityService;
import com.lenskart.nexs.common.entity.entityService.grn.GrnMasterEntityService;
import com.lenskart.nexs.common.entity.entityService.grn.GrnPidMasterEntityService;
import com.lenskart.nexs.common.entity.entityService.invoice.PurchaseInvoiceEntityService;
import com.lenskart.nexs.common.entity.entityServiceImpl.PurchaseOrderEntityServiceImpl;
import com.lenskart.nexs.common.entity.entityServiceImpl.grn.GrnItemEntityServiceImpl;
import com.lenskart.nexs.common.entity.entityServiceImpl.grn.GrnItemMetaDataEntityServiceImpl;
import com.lenskart.nexs.common.entity.po.PurchaseOrder;
import com.lenskart.nexs.common.entity.po.PurchaseOrderItem;
import com.lenskart.nexs.common.entity.po.grn.GrnItemEntity;
import com.lenskart.nexs.common.entity.po.grn.GrnItemMetaDataEntity;
import com.lenskart.nexs.common.entity.po.grn.GrnMasterEntity;
import com.lenskart.nexs.common.entity.po.grn.GrnPidMasterEntity;
import com.lenskart.nexs.common.entity.po.invoice.PurchaseInvoiceEntity;
import com.lenskart.nexs.common.model.request.catalog.ProductDetailModel;
import com.lenskart.nexs.common.model.request.catalog.TaxInvoiceDetailRequestModel;
import com.lenskart.nexs.common.model.request.grn.BarcodePriceRequest;
import com.lenskart.nexs.common.model.response.catalog.PidTaxDetail;
import com.lenskart.nexs.common.model.response.grn.BarcodePriceResponse;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.constants.RedisOps;
import com.lenskart.nexs.ems.enums.ExceptionType;
import com.lenskart.nexs.ems.enums.Source;
import com.lenskart.nexs.ems.model.AutoGrnItemScanDetails;
import com.lenskart.nexs.ems.model.AutoGrnScanDetails;
import com.lenskart.nexs.ems.model.EmsExceptionEvent;
import com.lenskart.nexs.ems.model.GrnJitScannedBarcodeEvent;
import com.lenskart.nexs.exception.CustomException;
import com.lenskart.nexs.fms.model.entity.LegalOwner;
import com.lenskart.nexs.fms.model.repo.LegalOwnerConfigRepository;
import com.lenskart.nexs.fms.model.response.FacilityDetailsResponse;
import com.lenskart.nexs.grn.client.InventoryTransferClient;
import com.lenskart.nexs.grn.config.ExpiryMonthMapping;
import com.lenskart.nexs.grn.config.GRNConfig;
import com.lenskart.nexs.grn.connector.UnicomConnector;
import com.lenskart.nexs.grn.constants.CSVHeaders;
import com.lenskart.nexs.grn.constants.GRNConstants;
import com.lenskart.nexs.grn.constants.GRNQcLogEvents;
import com.lenskart.nexs.grn.constants.QualifierConstants;
import com.lenskart.nexs.grn.dao.*;
import com.lenskart.nexs.grn.dto.request.GRNItemDTO;
import com.lenskart.nexs.grn.dto.request.UpdateGRNItemDTO;
import com.lenskart.nexs.grn.dto.response.*;
import com.lenskart.nexs.grn.entity.AccountBarcodePurchasePricing;
import com.lenskart.nexs.grn.entity.GrnItemsArchivedEntity;
import com.lenskart.nexs.grn.enums.ActionStatus;
import com.lenskart.nexs.grn.exception.ApplicationException;
import com.lenskart.nexs.grn.exception.enums.GRNExceptionStatus;
import com.lenskart.nexs.grn.model.*;
import com.lenskart.nexs.grn.putaway.service.PutawayService;
import com.lenskart.nexs.grn.repo.AccountBarcodePurchasePricingRepo;
import com.lenskart.nexs.grn.service.impl.GrnItemsArchivedEntityServiceImpl;
import com.lenskart.nexs.grn.service.strategy.BarcodeValidation;
import com.lenskart.nexs.grn.service.strategy.GRNQCValidation;
import com.lenskart.nexs.grn.util.*;
import com.lenskart.nexs.ims.request.BarcodePriceDetailsRequest;
import com.lenskart.nexs.ims.request.UpdateStockInwardRequest;
import com.lenskart.nexs.ims.request.UpdateStocksRequestV2;
import com.lenskart.nexs.ims.response.BarcodeInwardResponse;
import com.lenskart.nexs.ims.response.FetchBarcodeInwardResponse;
import com.lenskart.nexs.ims.response.UpdateStocksResponseV2;
import com.lenskart.nexs.inventoryBulkTransfer.request.FetchItemRequestModel;
import com.lenskart.nexs.po.common.serviceutil.connector.OrderOpsConnector;
import com.lenskart.nexs.putaway.constants.PutAwayAction;
import com.lenskart.nexs.putaway.model.request.CreatePutAwayRequest;
import com.lenskart.nexs.putaway.model.response.CreatePutawayItemResponse;
import com.lenskart.nexs.putaway.model.response.CreatePutawayResponse;
import com.lenskart.nexs.service.RedisHandler;
import com.nexs.po.common.enums.GrnImsSyncStatusEnum;
import com.nexs.po.common.enums.GrnPutawaySyncStatusEnum;
import com.nexs.po.common.enums.InvoiceLevelEnum;
import net.javacrumbs.shedlock.core.SchedulerLock;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.server.ResponseStatusException;

import javax.persistence.Tuple;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Component
public class GRNItemService implements GRNConstants, CSVHeaders {

	@Autowired
	@Qualifier(QualifierConstants.JPA_GRN_ITEM_DAO)
	private GRNItemDAO grnItemDAO;

	@Autowired
	@Qualifier(QualifierConstants.JPA_GRN_MASTER_DAO)
	private GRNMasterDAO grnMasterDAO;

	@Autowired
	private BarcodeValidation barcodeValidation;

	@Autowired
	private GRNCacheService grnCacheService;

	@Autowired
	private GRNQCValidation grnQCValidation;

	@Autowired
	@Qualifier(QualifierConstants.JPA_QC_STATUS_DAO)
	private QcStatusDAO qcStatusDAO;

	@Autowired
	private CacheUpdateService cacheUpdateService;

	@Autowired
	private CacheDAO cacheDAO;

	@Autowired
	private GRNQcLogService grnQcLogService;

	@Autowired
	@Qualifier(QualifierConstants.JPA_BOX_DAO)
	private BoxDAO boxDAO;

	@CustomLogger
	private Logger log;

	@Autowired
	private PutawayService putawayService;

	private static final String VALIDATION_STATUS = "validation_status";
	private static final String REASON = "reason";

	@Autowired
	private GRNConfig grnConfig;

	@Autowired
	private IMSService imsService;

	@Autowired
	private WmConnector wmConnector;

	@Autowired
	PurchaseInvoiceEntityService purchaseInvoiceEntityService;

	@Autowired
	GrnItemMetaDataEntityServiceImpl grnItemMetaDataEntityService;

	@Value("${kafka.topic.ems.exception.handler}")
	private String emsKafkaTopic;

	@Value("${jit.barcode.ems.sync.retry.count}")
	private String jitBarcodeEmsSyncRetry;

	@Autowired
	private GrnItemEntityServiceImpl grnItemEntityService;

	@Autowired
	private GrnItemsArchivedEntityServiceImpl grnItemsArchivedEntityService;

	@Autowired
	@Qualifier(QualifierConstants.JPA_GRN_PID_DAO)
	private GRNPIDDAO grnPidDao;

	@Autowired
	private PurchaseOrderItemEntityService purchaseOrderItemEntityService;

	@Autowired
	private GRNMasterService grnMasterService;

	@Autowired
	private GRNCacheServiceUtils grnCacheServiceUtils;

	@Autowired
	private GrnPidMasterEntityService grnPidMasterEntityService;

	@Autowired
	@Qualifier("categoryExpiryMonths")
	private Map<String, String> categoryExpiryMonths;

	@Autowired
	private GrnItemEntityService grnItemService;

	@Autowired
	private UnicomConnector unicomConnector;

	@Autowired
	private GrnMasterEntityService grnMasterEntityService;

	@Autowired
	private InventoryAdapterClient inventoryAdapterClient;

	@Autowired
	private InventoryTransferClient inventoryTransferClient;

	@Autowired
	private PurchaseOrderEntityServiceImpl purchaseOrderEntityServiceImpl;

	@Autowired
	private ExpiryMonthMapping expiryMonthMapping;

	@Value("${nexs.po.procurement.type.ttl.in.hrs}")
	private long poProcurementTypeTtl;

	@Value("${facility.code.list}")
	private Set<String> facilityCodeList;

	@Autowired
	private ObjectMapper objectMapper;

	@Autowired
	private FacilityDetailsUtils facilityDetailsUtils;

	@Autowired
	private OrderOpsConnector orderOpsConnector;

	@Autowired
	private LegalOwnerConfigRepository legalOwnerConfigRepository;

	@Value("${source.facility.for.tax.calculation}")
	private String sourceFacility;

	@Value("${fetch.barcode.price.from.unicom:true}")
	private boolean fetchBarcodePriceFromUnicom;

	@Value("${fetch.barcode.price.tax.enabled.currencies:INR}")
	private Set<String> taxEnabledCurrencies;

	@Autowired
	private AccountBarcodePurchasePricingRepo accountBarcodePurchasePricingRepo;

	@Logging
	public ScanItemResponseDTO scanGRNItem(GRNItemDTO grnItemDTO) throws Exception {

		log.info("Scanning barcode {} with grn code: {}", grnItemDTO.getBarcode(), grnItemDTO.getGrnCode());
		validateScanItemRequest(grnItemDTO);
		String hsnClassificationType = grnItemDTO.getType();

//		Validate Jit Po type Grn barcode Scan
		log.info("[validateJitPOType] Scan grn Item po {}, type {}", grnItemDTO.getPoID(), grnItemDTO.getType());
		grnMasterService.validatePOType(grnItemDTO.getPoID(), grnItemDTO.getBarcode(), grnItemDTO.getType());

		checkGrnStatus(grnItemDTO.getGrnCode(), grnItemDTO.getCreatedBy());
		validateConditionForExpiryDate(grnItemDTO.getBarcode(), grnItemDTO.getLegalOwner(), grnItemDTO.getPoID(),
					grnItemDTO.getType(), null, grnItemDTO.getProduct().getCategoryId().toString(),
					grnItemDTO.getExpiryDate());

		Boolean isGRNManualOverriden = qcStatusDAO.isGRNManualOverriden(grnItemDTO.getGrnCode(), grnItemDTO.getPid());

		GRNItem grnItem = GRNItemServiceUtils.convertDtoToGRNItem(grnItemDTO);
		if (!isGRNManualOverriden && qcStatusDAO.isQcStatusFailed(grnItemDTO.getGrnCode(), grnItemDTO.getPid())) {
			throw new ApplicationException("GRN pid qc failed, manual intervention required", GRNExceptionStatus.GRN_QC_FAILED);
		}

		UpdateStocksRequestV2 updateStocksRequestV2 = createUpdateStocksRequestV2(grnItemDTO, grnItem);
		BarcodeValidationResponse res = barcodeValidation.validateBarcode(grnItemDTO, updateStocksRequestV2);
		if (!res.isStatus()) {
			grnQcLogService.logGRNQcAction(grnItemDTO.getGrnCode(), grnItemDTO.getPid(), grnItemDTO.getBarcode(),
					res.getMessage().equals(BARCODE_DUPLICATE) ? GRNQcLogEvents.DUPLICATE : GRNQcLogEvents.INVALID);
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Scanned Barcode is " + res.getMessage());
		}

		try {
			log.info("Scan barcode, PO: {}, Pid:{}, GRN code: {}", grnItem.getPoId(), grnItem.getPid(),
					grnItem.getGrnCode());

			Map<String, Object> validationResult = grnQCValidation.validateGRNItem(grnItemDTO);
			if (!(Boolean) validationResult.get(VALIDATION_STATUS)) {
				grnItem.setQcStatus(QC_FAIL);
				grnItem.setQcReason((String) validationResult.getOrDefault(REASON, null));
			}

			grnCacheService.itemQuantityCheck(grnItem, hsnClassificationType, grnItemDTO.getInvoiceLevel());
			String oldStatus = qcStatusDAO.getGrnStatus(grnItem.getGrnCode(), grnItem.getPid());
//			QcConfig oldGrnConfig = cacheDAO.getGrnQcConfig(grnItem);
//			QcConfig oldInvoiceConfig = cacheDAO.getInvoiceQcConfig(grnItem.getInvoiceRefNum(), grnItem.getPid(), grnItem.getGrnCode());
			//In case of item status is changed from pass to fail than , box barcode should also be set as null
			if (QC_FAIL.equals(grnItem.getQcStatus()))
				grnItem.setQcPassBoxBarcode(null);
			ScanItemResponseDTO response = new ScanItemResponseDTO();

			log.info("[scanGRNItem] Create putaway barcode {}", grnItem.getBarcode());
			GrnItemEntity grnItemEntity = createPutawayAndSaveGrnItem(grnItemDTO, response, grnItem, oldStatus);

			log.info("[scanGRNItem] Send GRN item details in IMS, barcode {}, updateStocksRequestV2 {}",
					grnItem.getBarcode(), updateStocksRequestV2);

			createBarcodeInImsAndUpdateGrnItem(grnItemEntity,grnItemDTO.getBarcodeUrl());
			log.info("[scanGRNItem] Successfully status updated in Grn item for ims sync status ,barcode {}", grnItem.getBarcode());

			if (grnCacheService.updateCache(grnItem)) {
				long totalScanned = qcStatusDAO.getTotalScanned(grnItem.getGrnCode(), grnItem.getPid());
				long allFailed = qcStatusDAO.getAllFailed(grnItem.getGrnCode(), grnItem.getPid());
				long allScanned = qcStatusDAO.getAllScanned(grnItem.getGrnCode(), grnItem.getPid());
				GRNItemServiceUtils.convertToResponseDTO(grnItem, totalScanned, allScanned, allFailed, response);
				return response;
			} else {
				throw new ApplicationException("GRN pid qc failed, manual intervention required", GRNExceptionStatus.GRN_QC_FAILED);
			}
		} catch (Exception ex) {
			Throwable cause = ex;
			log.error("Exception: Scanning barcode {} , grn code: {} , error: {}", grnItemDTO.getBarcode(),
					grnItemDTO.getGrnCode(), ex.getMessage());
			deleteRedisCountKeyForInvoiceAndPO(grnItemDTO.getInvoiceRefNum(), grnItemDTO.getPoID(), grnItemDTO.getPid());
			while (cause != null) {
				if (cause instanceof org.hibernate.exception.ConstraintViolationException) {
					org.hibernate.exception.ConstraintViolationException constraintViolationException =
							(org.hibernate.exception.ConstraintViolationException) cause;
					String errorMessage = "Error code: " + constraintViolationException.getErrorCode() +
							", Message: " + constraintViolationException.getMessage() +
							", SQL Statement: " + constraintViolationException.getSQL() +
							", Constraint Name: " + constraintViolationException.getConstraintName();
					log.error("ConstraintViolationException: Scanning barcode {} , grn code: {} , error: {}",
							grnItemDTO.getBarcode(), grnItemDTO.getGrnCode(), errorMessage);
					if (constraintViolationException.getConstraintName().equalsIgnoreCase("unicom_po_id_barcode") ||
							constraintViolationException.getConstraintName().equalsIgnoreCase("po_id") )
						throw new ApplicationException("Duplicate Barcode " + grnItemDTO.getBarcode(), GRNExceptionStatus.GRN_BAD_REQUEST);
					else
						throw new ApplicationException("Error: " + errorMessage, GRNExceptionStatus.GRN_BAD_REQUEST);
				}
				cause = cause.getCause();
			}
			throw new ApplicationException("Unable to scan barcode " + grnItemDTO.getBarcode() + ", Error: " + ex.getMessage(),
					GRNExceptionStatus.GRN_BAD_REQUEST);
		}
	}

	private void createBarcodeInImsAndUpdateGrnItem(GrnItemEntity grnItemEntity, String barcodeUrl) throws CustomException {
		log.info("[createBarcodeInImsAndUpdateGrnItem] Ims sync status updated in Grn items, barcode {}",
				grnItemEntity.getBarcode());
		saveBarcodeDetailsInIms(grnItemEntity, GRN_QC_PASS, grnItemEntity.getFacility() + ".GOOD", barcodeUrl);
		grnItemEntity.setImsSyncStatus(GrnImsSyncStatusEnum.GRN_DONE);
		grnItemDAO.saveGrnItemEntity(grnItemEntity);
		log.info("[createBarcodeInImsAndUpdateGrnItem] Ims sync status updated in Grn items, barcode {}",
				grnItemEntity.getBarcode());
	}

	private GrnItemEntity createPutawayAndSaveGrnItem(GRNItemDTO grnItemDTO, ScanItemResponseDTO response, GRNItem grnItem, String oldStatus) {
		log.info("[createPutawayAndSaveGrnItem] barcode {}, grnCode {}",
				grnItemDTO.getBarcode(), grnItemDTO.getGrnCode());
		List<CreatePutawayResponse> putawayResponse = putawayService.createPutAway(grnItemDTO, response, null);
		log.info("[createPutawayAndSaveGrnItem] barcode {}, grnCode {}, putawayResponse {}",
				grnItemDTO.getBarcode(), grnItemDTO.getGrnCode(), putawayResponse);

		if(grnItemDTO.getPutawayCode() != null){
			grnItem.setPutawayCode(grnItemDTO.getPutawayCode());
			grnItem.setPutawaySyncStatus(GrnPutawaySyncStatusEnum.PUTAWAY_CREATED);
		}

		GrnItemEntity grnItemEntity = grnItemDAO.save(grnItem, oldStatus);
		log.info("[createPutawayAndSaveGrnItem] barcode {}, grnCode {}, Barcode Successfully saved in grn items {}",
				grnItemDTO.getBarcode(), grnItemDTO.getGrnCode(), grnItemEntity);
		return grnItemEntity;
	}

	private UpdateStocksRequestV2 createUpdateStocksRequestV2(GRNItemDTO grnItemDTO, GRNItem grnItem) {
		UpdateStocksRequestV2 updateStocksRequestV2 = new UpdateStocksRequestV2();
		String operation = null;
		String location = null;
		if (grnItemDTO.getSamplingPercent() != null && grnItemDTO.getSamplingPercent() != 0) {
			operation = GRNConstants.IQC_GRN_DONE;
			location = GRNConstants.IQC_GRN_DONE;
		} else {
			if ("pass".equalsIgnoreCase(grnItem.getQcStatus()))
				operation = GRNConstants.GRN_QC_PASS;
			else
				operation = GRNConstants.GRN_QC_FAIL;
			location = GRNConstants.GRN;
		}
		updateStocksRequestV2 = imsService.frameUpdateStockRequestForGRNItem
				(grnItem, ActionStatus.IMS_INSERT_GRN_ITEMS, location, operation);
		return updateStocksRequestV2;
	}

	private void validateConditionForExpiryDate(String barcode, String legalOwner, String poNum, String poType,
												String procurementType, String classificationId, Timestamp barcodeExpiryDate) {
		try {
			log.info("[validateConditionForExpiryDate] barcode {}", barcode);
			if (procurementType == null) procurementType = fetchProcurementType(barcode, poNum);
			log.info("[validateConditionForExpiryDate] barcode {}, legalOwner {}, poType {}, procurementType {}, classificationId {}",
					barcode, legalOwner, poType, procurementType, classificationId);

			Integer monthsToAdd = getExpiryMonthsToAdd(legalOwner, poType, procurementType, classificationId);
			if (monthsToAdd == null) {
				log.info("[validateConditionForExpiryDate] barcode {}, Months to add does not exist in expiry date mapping", barcode);
				return;
			} else if (monthsToAdd != 0 && barcodeExpiryDate == null) {
				throw new ApplicationException("Barcode expiry date is required, minimum expiry date should be greater than " + monthsToAdd + " months.",
						GRNExceptionStatus.GRN_BAD_REQUEST);
			}

			validateExpDateToMinExpExpiryDate(barcode, monthsToAdd, barcodeExpiryDate);
			log.info("[validateConditionForExpiryDate] barcode {}, Successfully validated", barcode);
		} catch (Exception e) {
			log.error("[validateConditionForExpiryDate] barcode {}, error {}", barcode, e.getMessage());
			throw new ApplicationException("Error: " + e.getMessage(),
					GRNExceptionStatus.GRN_BAD_REQUEST);
		}
	}

	private void validateExpDateToMinExpExpiryDate(String barcode, Integer monthsToAdd, Timestamp barcodeExpiryDate) {
		log.info("[validateExpDateToMinExpExpiryDate] barcode {}, monthsToAdd {}, barcodeExpiryDate {}",
				barcode, monthsToAdd, barcodeExpiryDate);
		LocalDateTime minimumExpectedExpiryDate = LocalDateTime.now().plusMonths(monthsToAdd);
		if (barcodeExpiryDate.toLocalDateTime().isBefore(minimumExpectedExpiryDate)) {
			log.error("[validateExpDateToMinExpExpiryDate] Expiry date is less than minimum expected expiry date for {}, expDate : {} " +
					"and minimumExpectedExpiryDate : {}", barcode, barcodeExpiryDate, minimumExpectedExpiryDate);
			throw new ApplicationException("Expiry date is less than minimum expected expiry date i.e " + monthsToAdd +" months.",
					GRNExceptionStatus.GRN_BAD_REQUEST);
		}
	}

	private String fetchProcurementType(String barcode, String poNum) {
		log.info("[fetchProcurementType] barcode {}, poId {}", barcode, poNum);
		String redisKey = poNum.toUpperCase() + "-PROCUREMENT-TYPE";
		return getProcurementTypeFromRedisOrDB(poNum, redisKey);
	}

	private Integer getExpiryMonthsToAdd(String legalOwner, String poType, String procurementType, String classificationId) {
		try {
			log.info("[getExpiryMonthsToAdd] legalOwner {}, poType {}, procurementType {}, classificationId {}",
					legalOwner, poType, procurementType, classificationId);
			Integer month = expiryMonthMapping.fetchExpiryMonths(legalOwner, poType, procurementType, classificationId);
			log.info("[getExpiryMonthsToAdd] legalOwner {}, poType {}, procurementType {}, classificationId {}",
					legalOwner, poType, procurementType, classificationId);
			return month;
		} catch (Exception e) {
			log.error("[getExpiryMonthsToAdd] legalOwner {}, poType {}, procurementType {}, classificationId {}",
					legalOwner, poType, procurementType, classificationId);
			throw new ApplicationException(e.getMessage(), GRNExceptionStatus.GRN_BAD_REQUEST);
		}
	}

	private void validateExpiryDate(String grnCode, Timestamp expiryDate, Product product, String poNum) {
		log.info("Validating expiry date for CL type grn {}, expiryDate {} ---poNum : {} --- product : {}",
				grnCode, expiryDate, poNum, product);
		if(expiryDate == null){
			log.error("Expiry date is mandatory for contact lens {}", grnCode);
			throw new ApplicationException("Expiry date is mandatory for CL",
					GRNExceptionStatus.GRN_BAD_REQUEST);
		}
		LocalDateTime expDate = expiryDate.toLocalDateTime();
		String category = product.getCategoryId().toString();
		LocalDateTime minimumExpectedExpiryDate = getMinimumExpectedExpiryDate(grnCode, category);
		log.info("minimumExpectedExpiryDate based on category : {}", minimumExpectedExpiryDate);
		if(expDate.isBefore(minimumExpectedExpiryDate)){
			String redisKey = poNum.toUpperCase() + "-PROCUREMENT-TYPE";
			String procurementType = getProcurementTypeFromRedisOrDB(poNum, redisKey);
			validateExpiryDateOnProcurementType(grnCode, expDate, procurementType);
		}
	}

	private void validateExpiryDateOnProcurementType(String grnCode, LocalDateTime barcodeExpiryDate, String procurementType) {
		LocalDateTime minimumExpectedExpiryDate = getMinimumExpectedExpiryDate(grnCode, procurementType);
		log.info("minimumExpectedExpiryDate based on procType : {}", minimumExpectedExpiryDate);
		if (barcodeExpiryDate.isBefore(minimumExpectedExpiryDate)) {
			log.error("Expiry date is less than minimum expected expiry date for {}, expDate : {} " +
					"and minimumExpectedExpiryDate : {}", grnCode, barcodeExpiryDate, minimumExpectedExpiryDate);
			throw new ApplicationException("Expiry date is less than minimum expected expiry date",
					GRNExceptionStatus.GRN_BAD_REQUEST);
		}
	}

	private LocalDateTime getMinimumExpectedExpiryDate(String grnCode, String categoryOrProcType) {
		Integer expiryDaysToAdd = getExpiryMonthsToAdd(grnCode, categoryOrProcType);
		log.info("expiryDaysToAdd : {} and grnCode : {}", expiryDaysToAdd, grnCode);
		LocalDateTime minimumExpectedExpiryDate = LocalDateTime.now().plusMonths(expiryDaysToAdd);
		return minimumExpectedExpiryDate;
	}

	private Integer getExpiryMonthsToAdd(String grnCode, String categoryOrProcType) {
		log.info("categoryOrProcType : {}  for grnCode : {} --- categoryExpiryMonths : {}", categoryOrProcType,
				grnCode, categoryExpiryMonths);
		if (categoryExpiryMonths.containsKey(categoryOrProcType))
			return Integer.valueOf(categoryExpiryMonths.get(categoryOrProcType));
		return Integer.valueOf(categoryExpiryMonths.get("CL"));
	}

	private String getProcurementTypeFromRedisOrDB(String poNum, String redisKey) {
		String procurementType = null;
		try {
			log.info("[getProcurementTypeFromRedisOrDB] poNum {}, redisKey {}", poNum, redisKey);
			if (checkRedisKey(redisKey)) {
				procurementType = (String) RedisHandler.redisOps(RedisOps.GET, redisKey);
			} else {
				com.lenskart.nexs.common.entity.po.PurchaseOrder purchaseOrder = purchaseOrderEntityServiceImpl.getPurchaseOrder(poNum);
				procurementType = purchaseOrder.getProcurementYype();
				log.info("[getProcurementTypeFromRedisOrDB] poNum {}, procurementType : {} : {}",
						purchaseOrder.getPoNum(), procurementType, purchaseOrder);
				RedisHandler.redisOps(RedisOps.SETVALUETTL, redisKey, procurementType, poProcurementTypeTtl, TimeUnit.HOURS);
			}
			log.info("[getProcurementTypeFromRedisOrDB] poNum {}, redisKey {}, procurementType {}", poNum, redisKey, procurementType);
		} catch (Exception e) {
			log.error("[getProcurementTypeFromRedisOrDB] poNum {}, Redis key {} procurementType: {}",
					poNum, redisKey, procurementType);
		}
		return procurementType;
	}

	private boolean checkRedisKey(String redisKey) {
		try{
			return RedisHandler.hasKey(redisKey);
		} catch (Exception e){
			log.error("[checkRedisKey:GRNItemService] Error while fetching redis key redisKey {}, error {}",
					redisKey, e.getMessage());
			return false;
		}
	}

	@Logging
	public ResponseDTO<Result<Map<String, Object>>> updateItem(UpdateGRNItemDTO updateGRNItemDTO) throws Exception {
		try {
			log.info("Updating barcode {} , grn code: {}", updateGRNItemDTO.getBarcode(), updateGRNItemDTO.getGrnCode());
			validateUpdateGRNItemRequest(updateGRNItemDTO);
			String hsnClassificationType = updateGRNItemDTO.getType();
			GRNItem grnItem = grnItemDAO.getGrnItemDetails(updateGRNItemDTO.getBarcode(), null);
			if (hsnClassificationType != null && hsnClassificationType.equalsIgnoreCase(GRNConstants.CL_TYPE))
				validateExpiryDate(updateGRNItemDTO.getGrnCode(), updateGRNItemDTO.getExpiryDate(),
						updateGRNItemDTO.getMeta().getInvoice().getPids().get(0), grnItem.getPoId());

			Boolean isGRNManualOverriden = qcStatusDAO.isGRNManualOverriden(updateGRNItemDTO.getGrnCode(), updateGRNItemDTO.getPid());
			if (!isGRNManualOverriden && qcStatusDAO.isQcStatusFailed(updateGRNItemDTO.getGrnCode(), updateGRNItemDTO.getPid()))
				throw new ApplicationException("GRN pid qc failed, manual intervention required", GRNExceptionStatus.GRN_QC_FAILED);

//			Since we dont use update barcode api flow
			if (grnItem == null)
				throw new ResponseStatusException(HttpStatus.NOT_FOUND, "No record found for barcode : " + updateGRNItemDTO.getBarcode());
			GRNMaster grnMaster = grnMasterDAO.getGRNMaster(grnItem.getGrnCode());
			if (grnMaster == null)
				throw new ResponseStatusException(HttpStatus.NOT_FOUND, "No GRN found for barcode : " + updateGRNItemDTO.getBarcode());
			grnItem = GRNItemServiceUtils.getGRNItem(grnMaster, grnItem);

			log.info("Update barcode, PO: {}, Pid:{}, GRN code: {}", grnItem.getPoId(), grnItem.getPid(),
					grnItem.getGrnCode());

			String oldStatus = qcStatusDAO.getGrnStatus(grnItem.getGrnCode(), grnItem.getPid());
//			QcConfig oldGrnConfig = cacheDAO.getGrnQcConfig(grnItem);
			long totalFailed = qcStatusDAO.getTotalFailed(grnItem.getGrnCode(), grnItem.getPid());
			long failed = qcStatusDAO.getAllFailed(grnItem.getGrnCode(), grnItem.getPid());
//			QcConfig oldInvoiceConfig = cacheDAO.getInvoiceQcConfig(grnItem.getInvoiceRefNum(), grnItem.getPid(), grnItem.getGrnCode());
			String status = updateGRNItemDTO.getGreenChannel() ? PASSED : PENDING;
			cacheUpdateService.updateCacheState(grnItem, status, UPDATE);
			grnItem.setQcFailCode(updateGRNItemDTO.getQcFailCode());
			grnItem.setQcReason(updateGRNItemDTO.getQcReason());
			grnItem.setQcStatus(updateGRNItemDTO.getQcStatus());
			grnItem.setQcPassBoxBarcode(updateGRNItemDTO.getBoxBarcode());
			grnItem.setExpiryDate(updateGRNItemDTO.getExpiryDate());

			boolean res = grnCacheService.updateCache(grnItem);
			String qcReason = updateGRNItemDTO.getQcReason() != null ? updateGRNItemDTO.getQcReason() : "NA";
			updateGRNItemDTO.setQcReason(qcReason);
			putawayService.updateItemDetailsInPutaway(updateGRNItemDTO);

			//In case of item status is changed from pass to fail than , box barcode should also be set as null
			if (QC_FAIL.equals(updateGRNItemDTO.getQcStatus())) {
				updateGRNItemDTO.setBoxBarcode(null);
			}

			grnItemDAO.updateGRNItem(updateGRNItemDTO, oldStatus, totalFailed, failed, grnItem);

			log.info("Send GRN item details in IMS, barcode {}, action {}", grnItem.getBarcode(),
					ActionStatus.IMS_UPDATE_GRN_ITEMS.getName());
			String operation = getOperation(grnItem.getQcStatus());

			GrnItemEntity grnItemEntity = grnItemDAO.convertGrnItemToGrnItemEntity(grnItem);
			UpdateStockInwardRequest updateStockInwardRequest = imsService.frameUpdateStockRequestForGRNItemV2(grnItemEntity, operation);
			log.info("[updateItem], barcode {}, updateStockInwardRequest {}", grnItem.getBarcode(), updateStockInwardRequest);
			UpdateStocksResponseV2 updateStocksResponseV2 = imsService.performStockInward(updateStockInwardRequest);
			log.info("Sent GRN item details in IMS, barcode {}, response {}", grnItem.getBarcode(), updateStocksResponseV2);

			Result<Map<String, Object>> result = new Result<>();
			result.setMeta(updateGRNItemDTO.getMeta());
			if (res)
				return new ResponseDTO<>(result);
			else
				throw new ApplicationException("GRN pid qc failed, manual intervention required", GRNExceptionStatus.GRN_QC_FAILED);
		} catch (Exception ex) {
			log.error("Exception: Updating barcode {} , grn code: {} , error: {}",updateGRNItemDTO.getBarcode(),
					updateGRNItemDTO.getGrnCode(),ex.getMessage());
			deleteRedisCountKeyForInvoiceAndPO( updateGRNItemDTO.getMeta().getInvoice().getInvoiceRefNum(),
					updateGRNItemDTO.getMeta().getPo().getPoId(), updateGRNItemDTO.getPid());
			throw ex;
		}
	}

	public String getOperation(String status) {
		String operation = null;
		if ("pass".equalsIgnoreCase(status))
			operation = GRNConstants.GRN_QC_PASS;
		else
			operation = GRNConstants.GRN_QC_FAIL;
		return operation;
	}

	@Logging
	public GRNItem searchItem(String barcode, String facilityCode, boolean isBarcodeVendorInvoiceNumberRequired) throws Exception {
		log.info("[searchItem] barcode {}, facilityCode {}, isBarcodeVendorInvoiceNumberRequired {}",
				barcode, facilityCode, isBarcodeVendorInvoiceNumberRequired);
		if(StringUtils.isBlank(barcode)) {
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Item barcode can not be null or empty");
		}

		GRNItem grnItem;
		try {
			grnItem = getGrnItemFromNexs(barcode, facilityCode, isBarcodeVendorInvoiceNumberRequired);
		} catch (ResponseStatusException | ApplicationException exception) {
			log.info("GRN Item by barcode {}, NOT_FOUND in NEXS DB. Checking same in Athena.", barcode);
			grnItem = getGrnItemFromAthena(barcode);
		} catch (Exception exception) {
			log.error("fetch GRN Item for barcode {} failed with exception {}", barcode, exception);
			throw exception;
		}

		log.info("GRN Item response for barcode {} is {}", barcode, grnItem);
		return grnItem;
	}

	private GRNItem getGrnItemFromAthena(String barcode) throws Exception {
		return wmConnector.getGrnItemByBarcode(barcode);
	}

	private GRNItem getGrnItemFromNexs(String barcode, String facilityCode,
									   boolean isBarcodeVendorInvoiceNumberRequired) throws Exception {
		log.info("[getGrnItemFromNexs] barcode {}, facilityCode {}, isBarcodeVendorInvoiceNumberRequired {}",
				barcode, facilityCode, isBarcodeVendorInvoiceNumberRequired);
		GRNItem grnItem = grnItemDAO.getBarcodeDetails(barcode, facilityCode);
		if(grnItem == null) {
			log.info("[getGrnItemFromNexs] GRN Item NOT_FOUND for barcode {}", barcode);
			throw new ResponseStatusException(HttpStatus.NOT_FOUND, "No record found for barcode : " + barcode);
		}

		GRNMaster grnMaster = grnMasterDAO.getGRNMaster(grnItem.getGrnCode());
		if(grnMaster == null) {
			log.info("[getGrnItemFromNexs] GRN Master NOT_FOUND for barcode {} and GRN code {}", barcode, grnItem.getGrnCode());
			throw new ResponseStatusException(HttpStatus.NOT_FOUND, "No GRN found for barcode : " + barcode);
		}

		grnItem = GRNItemServiceUtils.getGRNItem(grnMaster, grnItem);
		if(isBarcodeVendorInvoiceNumberRequired) {
			grnItem.setInvoiceId(grnMaster.getInvoice().getInvoiceId());
		}
		return grnItem;
	}

	@Logging
	public void deleteItem(String barcode, String type, String invoiceLevel, String poId) throws Exception {
		log.info("Deleting barcode {}, type {}", barcode, type);
		if (StringUtils.isBlank(barcode))
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Item barcode can not be null or empty");

		GrnItemEntity grnItem = grnItemDAO.getGrnItemEntity(barcode, poId);
		if (grnItem == null)
			throw new ResponseStatusException(HttpStatus.NOT_FOUND, "ERROR: No record found for barcode : " + barcode);

		try {
			log.info("Delete barcode, PO: {}, Pid:{}, GRN code: {}", grnItem.getPoId(), grnItem.getPid(),
					grnItem.getGrnCode());

			Boolean isGRNManualOverriden = qcStatusDAO.isGRNManualOverriden(grnItem.getGrnCode(), grnItem.getPid());
			if (!isGRNManualOverriden && qcStatusDAO.isQcStatusFailed(grnItem.getGrnCode(), grnItem.getPid()))
				throw new ApplicationException("GRN pid qc failed, manual intervention required", GRNExceptionStatus.GRN_QC_FAILED);

			putawayService.dicardItemInPutaway(grnItem);
			int deleted = grnItemDAO.deleteItem(barcode, grnItem.getPoId());
			if (deleted <= 0) {
				log.error("Unable to delete for grn code {}, barcode {}", grnItem.getGrnCode(), barcode);
				throw new ResponseStatusException(HttpStatus.NOT_FOUND, "No record found for barcode : " + barcode);
			} else {
				log.info("Send Delete GRN item details in IMS, barcode {}, action {}", grnItem.getBarcode(),
						ActionStatus.IMS_DELETE_GRN_ITEMS.getName());
				String operation = GRNConstants.DISCARDED;
				UpdateStockInwardRequest updateStockInwardRequest = imsService.frameUpdateStockRequestForGRNItemV2(
						grnItem, operation);
				imsService.performStockInward(updateStockInwardRequest);
				log.info("Successfully Sent GRN item details in IMS, barcode {}, action {}", grnItem.getBarcode(),
						ActionStatus.IMS_DELETE_GRN_ITEMS.getName());
				if (InvoiceLevelEnum.SUMMARY.name().equalsIgnoreCase(invoiceLevel)) {
					log.info("[deleteItem] Deleting invoice grn key {}", grnItem.getBarcode());
					grnCacheService.deleteClInvoiceGrnKey(grnItem);
				} else {
					cacheUpdateService.updateCacheState(GRNItemServiceUtils.buildGrnItem(grnItem), 
							PENDING, DELETE_PO_INVOICE_COUNT);
				}
				grnQcLogService.logGRNQcAction(grnItem.getGrnCode(), grnItem.getPid(), barcode, GRNQcLogEvents.DELETE);
			}
		} catch (Exception ex) {
			log.error("Exception: Deleting barcode PO: {} ,PId: {}, Grn Code: {} ,Barcode: {}, Error: {}", grnItem.getPoId(), grnItem.getPid(),
					grnItem.getGrnCode(), barcode, ex.getMessage());
			deleteRedisCountKeyForInvoiceAndPO(grnItem.getInvoiceRefNum(), grnItem.getPoId(), grnItem.getPid());
			throw ex;
		}
	}

	@Logging
	public Map<String, Map<String, Object>> getGRNPidDetails(List<GRNPIDMaster> grnPids) {

		Map<String, Map<String, Object>> grnPidCounts = null;

		if (!CollectionUtils.isEmpty(grnPids)) {
			grnPidCounts = new HashMap<>();
			Map<String, List<String>> grnPidsMap = new HashMap<>();
			for (GRNPIDMaster grnPidMaster : grnPids) {
				if (grnPidsMap.containsKey(grnPidMaster.getGrnCode())) {
					grnPidsMap.get(grnPidMaster.getGrnCode()).add(grnPidMaster.getPid());
				} else {
					List<String> pids = new ArrayList<>();
					pids.add(grnPidMaster.getPid());
					grnPidsMap.put(grnPidMaster.getGrnCode(), pids);
				}
			}
			for (Entry<String, List<String>> entry : grnPidsMap.entrySet()) {

				Map<String, Object> pidCounts = grnItemDAO.getGRNItemScannedCount(entry.getKey(), entry.getValue());
				grnPidCounts.put(entry.getKey(), pidCounts);
			}
		}
		return grnPidCounts;
	}

	@Logging
	public Map<String, Object> getTotalScanByInvoice(String invoiceId, String pid) {

		Map<String, Object> pidList = grnItemDAO.getPIDTotalScanByInvoice(invoiceId, pid);
		if(CollectionUtils.isEmpty(pidList)) {
			throw new ResponseStatusException(HttpStatus.NO_CONTENT, "No data found for given invoice and pid");
		}

		return pidList;
	}

	public void exportGRNBarcodeDetails(String grnCode, HttpServletResponse response) throws IOException {

		log.info("Inside exportGRNBarcodeDetails with grn code : " + grnCode);
		List<GRNPidBarcodeDetailsDTO> grnBarcodeDetails = grnItemDAO.getGrnPidBarcodeDetails(grnCode);
		if (CollectionUtils.isEmpty(grnBarcodeDetails)) {
			log.info("No data found for grn : " + grnCode);
			throw new ResponseStatusException(HttpStatus.NO_CONTENT, "No data found");
		}

		String filename = grnCode + "-barcode-details.csv";
		CSVPrinter csvPrinter = null;
		try {
			response.setContentType("text/csv");
			response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"");
			csvPrinter = new CSVPrinter(response.getWriter(),
					CSVFormat.DEFAULT.withHeader(BarcodeDetailsCSV.HEADER1, BarcodeDetailsCSV.HEADER2, BarcodeDetailsCSV.HEADER3,
							BarcodeDetailsCSV.HEADER4, BarcodeDetailsCSV.HEADER5, BarcodeDetailsCSV.HEADER6, BarcodeDetailsCSV.HEADER7,
							BarcodeDetailsCSV.HEADER8));
			for (GRNPidBarcodeDetailsDTO grnBarcodeDetail : grnBarcodeDetails) {
				csvPrinter.printRecord(Arrays.asList(grnBarcodeDetail.getPid(), grnBarcodeDetail.getBarcode(), grnBarcodeDetail.getQcStatus(),
						grnBarcodeDetail.getQcReason(), grnBarcodeDetail.getScannedBy(), grnBarcodeDetail.getScannedAt(), grnBarcodeDetail.getPrice(),
						grnBarcodeDetail.getTaxRate()));
			}
		} catch (IOException ex) {
			log.error(ex.getMessage());
			throw ex;
		} catch (Exception ex) {
			log.error(ex.getMessage());
			throw new ApplicationException("Internal API Error", GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		} finally {
			if (csvPrinter != null) {
				csvPrinter.close();
			}
		}
	}

	private void validateScanItemRequest(GRNItemDTO grnItemDTO) {

		if (!(QC_FAIL.equals(grnItemDTO.getQcStatus()) || QC_PASS.equals(grnItemDTO.getQcStatus()))) {
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid qc status");
		}

		if (QC_FAIL.equals(grnItemDTO.getQcStatus())
				&& (StringUtils.isBlank(grnItemDTO.getQcFailCode()) || StringUtils.isBlank(grnItemDTO.getQcReason()))) {
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid qc fail code or reason");
		}

		if ((cacheDAO.isBoxRequired(grnItemDTO.getGrnCode(), grnItemDTO.getPid()) && QC_PASS.equals(grnItemDTO.getQcStatus()))
				&& StringUtils.isBlank(grnItemDTO.getQcPassBoxBarcode())) {
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Box barcode required for qc pass items");
		}

		if (!StringUtils.containsIgnoreCase(grnItemDTO.getGrnCode(), grnItemDTO.getFacilityCode())) {
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Please select the correct facility code");
		}
	}

	private void validateUpdateGRNItemRequest(UpdateGRNItemDTO updateGRNItemDTO) {

		if (QC_PASS.equals(updateGRNItemDTO.getQcStatus()) && cacheDAO.isBoxRequired(updateGRNItemDTO.getGrnCode(), updateGRNItemDTO.getPid())
				&& StringUtils.isBlank(updateGRNItemDTO.getBoxBarcode())) {
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Box barcode is required for qc passed item");
		}

		if (QC_FAIL.equals(updateGRNItemDTO.getQcStatus())
				&& (StringUtils.isBlank(updateGRNItemDTO.getQcFailCode()) || StringUtils.isBlank(updateGRNItemDTO.getQcReason()))) {
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Qc fail code or qc reason cannot be blank for qc failed item");
		}

		if (StringUtils.isNotBlank(updateGRNItemDTO.getBoxBarcode())
				&& !boxDAO.checkBoxExists(updateGRNItemDTO.getGrnCode(), updateGRNItemDTO.getPid(), updateGRNItemDTO.getBoxBarcode())) {

			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid box barcode");
		}

		Product productInfo = CommonUtils.getProductDetailsFromInvoice(updateGRNItemDTO.getMeta().getInvoice().getPids(), updateGRNItemDTO.getPid());
		if (productInfo.getCategoryId() == CONTACT_LENS && updateGRNItemDTO.getExpiryDate() == null) {
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Expiry date cannnot be Null for this pid");
		}

		if (!StringUtils.containsIgnoreCase(updateGRNItemDTO.getGrnCode(), updateGRNItemDTO.getFacilityCode())) {
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Please select the correct facility code");
		}

	}

	public Map<String, Long> resetRedisCounter(List<String> invoiceRefNumbers, List<String> poIds) {
		Map<String, Long> resetRedisCounter = new HashMap<>();
		List<PidItemCounter> invoicePidCount = grnItemDAO.getPidItemCount(invoiceRefNumbers, poIds);
		log.info("Invoice PID counter resultset is : {}", invoicePidCount);
		for (PidItemCounter invoicePidCounter : invoicePidCount) {
			qcStatusDAO.resetInvoiceQtyCount(invoicePidCounter, resetRedisCounter);
		}
		return resetRedisCounter;
	}

	@Logging
	public void deleteRedisCountKeyForInvoiceAndPO(String invoiceRefNum, String poId, String pid) {
		log.info("DELETING Redis Key for poId: {}, pid: {}, invoiceRefNum: {}", poId, pid, invoiceRefNum);
		qcStatusDAO.deleteRedisCountKeyForInvoiceAndPO(invoiceRefNum, poId, pid);
	}

	public Boolean validateDuplicateBarcode(String barcode, String facilityCode) {
		try {
			GRNItem item = grnItemDAO.getTopGrnItemDetailForBarcodeAndFacility(barcode, facilityCode);
			log.info("[validateDuplicateBarcode] record found for barcode {} and facility {} : {}", barcode,
					facilityCode, item);
			return true;
		} catch (Exception e) {
			log.error("Error in validating barcode {}, error {}", barcode, e.getMessage());
			return false;
		}
	}

	@Transactional(rollbackFor = Exception.class)
	public GRNItem scanGrnItemBarcode(GRNScanItemRequest grnScanItemRequest) {
		try{
			log.info("[scanGrnItemBarcode] Scan grn barcode {}, request {}", grnScanItemRequest.getBarcode(), grnScanItemRequest);
			if(grnScanItemRequest.getUwItemId() == null || grnScanItemRequest.getUwItemId().isEmpty()){
				log.error("[scanGrnItemBarcode] Scan grn barcode {}. UW item can not be null.", grnScanItemRequest.getBarcode());
				throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "UW item can not be null");
			}

			log.info("[scanGrnItemBarcode] Validate grn status {}", grnScanItemRequest.getGrnCode());
			checkGrnStatus(grnScanItemRequest.getGrnCode(), grnScanItemRequest.getCreatedBy());

			PurchaseInvoiceEntity purchaseInvoice = purchaseInvoiceEntityService.
					findByVendorInvoiceNumber(grnScanItemRequest.getVendorInvoiceNumber());
			if (!StringUtils.equalsIgnoreCase(grnScanItemRequest.getFacilityCode(),
					purchaseInvoice.getFacilityCode())) {
				log.error("[scanGrnItemBarcode] For barcode {} wrong facility code is passed {}",
						grnScanItemRequest.getBarcode(), grnScanItemRequest.getFacilityCode());
				throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Wrong facility passed in the request");
			}

			String type = grnScanItemRequest.getType() == null ? GRNConstants.MANUAL_JIT : grnScanItemRequest.getType();
			log.info("[scanGrnItemBarcode] barcode {}, facility {}, type {}",
					grnScanItemRequest.getBarcode(), grnScanItemRequest.getFacilityCode(), type);

			boolean barcodeSuccess = barcodeValidation.validateBarcodeSuccess(grnScanItemRequest.getBarcode(),
					grnScanItemRequest.getFacilityCode(), type, Integer.parseInt(grnScanItemRequest.getProductId()),
					GRNConstants.GRN_QC_PASS, null);
			log.info("[scanGrnItemBarcode] barcode {}, facility {}, type MANUAL_JIT, pid {}, barcodeSuccess {}",
					grnScanItemRequest.getBarcode(), grnScanItemRequest.getFacilityCode(),
					Integer.parseInt(grnScanItemRequest.getProductId()), barcodeSuccess);
			if (!barcodeSuccess) {
				throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Barcode validation failed!!!");
			}

			GRNItem grnItem = createGrnItem(grnScanItemRequest, purchaseInvoice);
			GrnItemEntity grnItemDetails = grnItemDAO.saveGrnItemDetails(grnItem);

			log.info("[scanGrnItemBarcode] Storing details in grn item meta data barcode {}, fitting id {}," +
							" uw item item id {}", grnScanItemRequest.getBarcode(), grnScanItemRequest.getFittingId(),
					grnScanItemRequest.getUwItemId());
			List<GrnItemMetaDataEntity> grnItemMetaDataEntities = saveGrnItemMetaDataDetails(grnScanItemRequest);

			log.info("[scanGrnItemBarcode] Storing details in ims barcode {}, fitting id {}, uw item item id {}",
					grnScanItemRequest.getBarcode(), grnScanItemRequest.getFittingId(), grnScanItemRequest.getUwItemId());

			String operation = null;
			if ("pass".equalsIgnoreCase(grnItem.getQcStatus()))
				operation= GRNConstants.GRN_QC_PASS;
			else
				operation = GRNConstants.GRN_QC_FAIL;
			log.info("[scanGrnItemBarcode] Storing details in ims barcode {}, fitting id {}, uw item item id {}, " +
							"grnItem {}, operation {}", grnScanItemRequest.getBarcode(), grnScanItemRequest.getFittingId(),
					grnScanItemRequest.getUwItemId(), grnItem, operation);
			saveBarcodeDetailsInIms(grnItemDetails, operation, grnItem.getFacility() + ".JIT.GOOD", grnScanItemRequest.getBarcodeUrl());

			log.info("[scanGrnItemBarcode] Add Details in GRN pid master {}", grnScanItemRequest.getBarcode());
			addGrnPidMaster(grnItem, purchaseInvoice);
			log.info("[scanGrnItemBarcode] Successfully added GRN pid master {}", grnScanItemRequest.getBarcode());

			try {
				log.info("[scanGrnItemBarcode] Push message to kafka {}", grnScanItemRequest.getBarcode());
				if (grnScanItemRequest.isEmsSync()) {
					pushMessageToEmsKafka(grnScanItemRequest);
				}
			} catch (Exception e){
				log.error("[scanGrnItemBarcode] Unable to send data in EMS {}, {}", grnScanItemRequest.getBarcode(), e.getMessage());
				saveGrnItemMetaData(grnScanItemRequest.getBarcode(), EMS_SYNCED_RETRY_COUNT,
						"0", grnScanItemRequest.getCreatedBy());
				return grnItem;
			}

			for(GrnItemMetaDataEntity grnItemMetaData: grnItemMetaDataEntities){
				if(grnItemMetaData.getKey().equalsIgnoreCase(SYNCED_STATUS_META_DATA)){
					grnItemMetaData.setValue("1");
					grnItemMetaDataEntityService.saveOrUpdate(grnItemMetaData);
				}
			}

			log.info("[scanGrnItemBarcode] Validate data to close grn {}, MDC user {}", grnScanItemRequest.getBarcode(), MDC.get(USER_ID));
			if(MDC.get(USER_ID) == null || MDC.get(USER_ID).isEmpty())
				MDC.put("USER_ID", "VSM");
			validateCloseGrnCondition(grnItem, purchaseInvoice);
			log.info("[scanGrnItemBarcode] Grn item data successfully saved {}", grnScanItemRequest.getBarcode());
			return grnItem;
		} catch (Exception e){
			log.error("Error in scanning barcode {}, error {}", grnScanItemRequest, e.getMessage());
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Exception to scan barcode: " + e.getMessage());
		}
	}

	public GrnMasterEntity checkGrnStatus(String grnCode, String createdBy) throws Exception {
		if (StringUtils.isBlank(grnCode)) {
			log.error("Grn code can not be empty {}", grnCode);
			throw new Exception("Grn can not be empty");
		}

		GrnMasterEntity grnMasterEntity = grnMasterEntityService.findByGrnCode(grnCode);
		if (grnMasterEntity == null){
			log.error("[checkGrnStatus] Grn does not exist for {}", grnCode);
			throw new Exception("Grn does not exist for " + grnCode);
		}
		if (!StringUtils.equalsAnyIgnoreCase(grnMasterEntity.getGrnStatus(), "created", "in_progress")) {
			log.error("[checkGrnStatus] GRN is not in a valid state {} : {}", grnCode, grnMasterEntity.getGrnStatus());
			throw new Exception("GRN is not in a valid state for barcode scanning. Current state: " + grnMasterEntity.getGrnStatus() + ", GrnCode: " + grnCode);
		}
		if (!StringUtils.equalsIgnoreCase(createdBy, grnMasterEntity.getCreatedBy())) {
			log.error("[checkGrnStatus] GRN can be scanned only by user {} : {}", createdBy, grnCode);
			throw new Exception("GRN can be scanned only by user: " + createdBy);
		}
		log.info("Grn {} is in {} state : {}", grnCode, grnMasterEntity.getGrnStatus(), grnMasterEntity);
		return grnMasterEntity;
	}

	private void validateCloseGrnCondition(GRNItem grnItem, PurchaseInvoiceEntity purchaseInvoice) {
		try{
			log.info("[validateCloseGrnCondition] barcode {}, grnCode {}", grnItem.getBarcode(), grnItem.getGrnCode());
			long totalGrnScanCount = purchaseInvoice.getTotalInvoiceQty();
			long grnScannedQty = grnCacheServiceUtils.fetchGrnCodeScannedItem
					(String.valueOf(purchaseInvoice.getInvoiceRefNumber()), grnItem.getGrnCode());
			log.info("totalGrnScanCount {}, grnScannedQty {}", totalGrnScanCount, grnScannedQty);
			if(grnScannedQty == totalGrnScanCount){
				log.info("Closing jit grn for barcode {}, grn code  {}",
						grnItem.getBarcode(), grnItem.getGrnCode());
				GRNMaster grnMaster = grnMasterService.getGRNMaster(grnItem.getGrnCode());
				grnMasterService.closeGRN(grnItem.getGrnCode(), grnItem.getFacility(), grnMaster, true);
			}
			log.info("[validateCloseGrnCondition] Condition validated barcode {}, grnCode {}",
					grnItem.getBarcode(), grnItem.getGrnCode());
		} catch (Exception e){
			log.error("[validateCloseGrnCondition] Unable to close GRN barcode {}, grnCode {}, error {}",
					grnItem.getBarcode(), grnItem.getGrnCode(), e.getMessage());
		}
	}

	private GRNItem createGrnItem(GRNScanItemRequest grnScanItemRequest, PurchaseInvoiceEntity purchaseInvoice) {
		try{
			log.info("[createGrnItem] Create grn item {}", grnScanItemRequest.getBarcode());
			GRNItem grnItem = new GRNItem();
			grnItem.setBarcode(grnScanItemRequest.getBarcode());
			grnItem.setGrnCode(grnScanItemRequest.getGrnCode());
			grnItem.setStatus(GRNConstants.PASSED);
			grnItem.setPoId(purchaseInvoice.getPoNum());
			grnItem.setPid(grnScanItemRequest.getProductId());
			grnItem.setInvoiceRefNum(Integer.toString(purchaseInvoice.getInvoiceRefNumber()));
			grnItem.setVendorCode(grnScanItemRequest.getVendorCode());
			if(grnScanItemRequest.getExpiryDate() != null)
				grnItem.setExpiryDate(new Timestamp(grnScanItemRequest.getExpiryDate().getTime()));
			grnItem.setGrnEstimatedQuantity(0);
			grnItem.setQcStatus(grnScanItemRequest.getQcStatus());
			grnItem.setQcFailCode(grnScanItemRequest.getQcFailCode());
			grnItem.setQcReason(grnScanItemRequest.getQcReason());
			grnItem.setQcMasterSampling(0);
			grnItem.setQcGradientSampling(0);
			grnItem.setFailureThresholdQcMaster(0);
			grnItem.setFailureThresholdQcGradient(0);
			grnItem.setFacility(grnScanItemRequest.getFacilityCode());
			grnItem.setCreatedBy(grnScanItemRequest.getCreatedBy());
			grnItem.setUpdatedBy(grnScanItemRequest.getCreatedBy());
			grnItem.setCreatedAt(new Timestamp(new Date().getTime()));
			grnItem.setUpdatedAt(new Timestamp(new Date().getTime()));
			grnItem.setChannelStatus(1);
			grnItem.setLegalOwner(purchaseInvoice.getLegalOwner());
			log.info("[createGrnItem] Create grn item {}", grnScanItemRequest.getBarcode());
			return grnItem;
		} catch (Exception e){
			log.error("Unable to store grn items details {}", grnScanItemRequest.getBarcode());
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, e.getMessage());
		}
	}

	private List<GrnItemMetaDataEntity> saveGrnItemMetaDataDetails(GRNScanItemRequest request) {
		try{
			log.info("[saveGrnItemMetaDataDetails] grnScanItemRequest {}", request);
			List<GrnItemMetaDataEntity> entities = new ArrayList<>();
			String barcode = request.getBarcode();
			String createdBy = request.getCreatedBy();
			entities.add(saveGrnItemMetaData(barcode, FITTING_ID_META_DATA, request.getFittingId(), createdBy));
			entities.add(saveGrnItemMetaData(barcode, UW_ITEM_ID_META_DATA, request.getUwItemId(), createdBy));
			entities.add(saveGrnItemMetaData(barcode, SYNCED_STATUS_META_DATA, "0", createdBy));
			entities.add(saveGrnItemMetaData(barcode, PID_SWAP_STATUS_META_DATA, String.valueOf(request.isPidSwapAllow()), createdBy));
			return entities;
		} catch (Exception e){
			log.error("Error: {}, {}",
					request.getBarcode(), e.getMessage());
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, e.getMessage());
		}
	}

	private GrnItemMetaDataEntity saveGrnItemMetaData(String barcode, String key, String value, String createdBy) {
		try{
			log.info("[saveGrnItemMetaData] grnScanItemRequest {}, {}", key, value);
			GrnItemMetaDataEntity entity = new GrnItemMetaDataEntity();
			entity.setBarcode(barcode);
			entity.setKey(key);
			entity.setValue(value);
			entity.setEnabled(true);
			entity.setCreatedBy(createdBy);
			entity.setUpdatedBy(createdBy);
			entity.setCreatedAt(new Date());
			entity.setUpdatedAt(new Date());
			grnItemMetaDataEntityService.saveOrUpdate(entity);
			return entity;
		} catch (Exception e){
			log.error("Error in storing grn item meta data {}, {}",
					key, e.getMessage());
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, e.getMessage());
		}
	}

	private void saveBarcodeDetailsInIms(GrnItemEntity grnItemDetails, String operation, String location, String barcodeUrl) throws ResponseStatusException {
		try {
			log.info("[saveBarcodeDetailsInIms] Send GRN item details in IMS, barcode {}, action {}, operation {}, location {}", 
					grnItemDetails.getBarcode(), ActionStatus.IMS_INSERT_GRN_ITEMS.getName(), operation, location);
			UpdateStockInwardRequest updateStockInwardRequest = imsService.frameUpdateStockRequestForGRNItemV2(grnItemDetails,
					operation);
			updateBarcodeUrlIfpresent(barcodeUrl, updateStockInwardRequest);
			imsService.performStockInward(updateStockInwardRequest);
			log.info("[saveBarcodeDetailsInIms] Sent GRN item details in IMS, barcode {}, updateStockInwardRequest {}",
							grnItemDetails.getBarcode(), updateStockInwardRequest);
		} catch (Exception e) {
			log.error("[saveBarcodeDetailsInIms] Unable to store details in IMS {}, {}", grnItemDetails.getBarcode(),
					e.getMessage());
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, e.getMessage());
		}
	}

	private static void updateBarcodeUrlIfpresent(String barcodeUrl, UpdateStockInwardRequest updateStockInwardRequest) {
		if (!StringUtils.isBlank(barcodeUrl) && !updateStockInwardRequest.getStockInwardRequestList().isEmpty() && updateStockInwardRequest.getStockInwardRequestList().get(0) != null) {
			updateStockInwardRequest.getStockInwardRequestList().get(0).setBarcodeUrl(barcodeUrl);
		}
	}

	public void pushMessageToEmsKafka(GRNScanItemRequest grnScanItemRequest) {
		try {
			log.info("Push message to kafka {}", grnScanItemRequest.getBarcode());
			GrnJitScannedBarcodeEvent event = new GrnJitScannedBarcodeEvent();
			event.setFittingId(grnScanItemRequest.getFittingId());
			event.setUwItemId(grnScanItemRequest.getUwItemId());
			event.setBarcode(grnScanItemRequest.getBarcode());
			event.setGrnCode(grnScanItemRequest.getGrnCode());
			event.setProductId(grnScanItemRequest.getProductId());
			event.setFacilityCode(grnScanItemRequest.getFacilityCode());
			event.setQcStatus(grnScanItemRequest.getQcStatus());
			event.setPidSwapAllow(grnScanItemRequest.isPidSwapAllow());
			log.info("Push message to kafka {}", event);

			Map<String, Object> headers = new HashMap<>();
			ObjectMapper objectMapper=new ObjectMapper();
			EmsExceptionEvent emsExceptionEvent = new EmsExceptionEvent();
			emsExceptionEvent.setCreatedAt(new Date());
			emsExceptionEvent.setSource(Source.GRN);
			emsExceptionEvent.setExceptionType(ExceptionType.JIT_GRN_BARCODE_SCANNED);
			emsExceptionEvent.setMeta(objectMapper.writeValueAsString(event));

			headers.put("USER_ID", grnScanItemRequest.getCreatedBy());
			headers.put("USER_MAIL", grnScanItemRequest.getCreatedBy());
			headers.put("FACILITY_CODE", grnScanItemRequest.getFacilityCode());
			headers.put("EVENT_IDEMPOTENCY_KEY", grnScanItemRequest.getGrnCode() + "_" + grnScanItemRequest.getBarcode());
			log.info("Producing a message to ems kafka producer for barcode {} , message {}, with headers: {}",
					grnScanItemRequest.getBarcode(), emsExceptionEvent, headers);
			RetryUtils.sendMessageWithPartitionKey(emsExceptionEvent,
					emsKafkaTopic, headers, "JIT_GRN_BARCODE_SCANNED");

			log.info("Message successfully pushed to ems");
		} catch (Exception e){
			log.error("Unable to push message to ems {}: error {}",
					grnScanItemRequest.getBarcode(), e.getMessage());
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, e.getMessage());
		}
	}

	private Map<String, BarcodePriceResponse> getBarcodePriceMap(String grnCode, PurchaseOrder purchaseOrder,
			List<GrnItemEntity> grnEntities) throws Exception {
		BarcodePriceRequest barcodePriceRequest = new BarcodePriceRequest();
		Map<String, Integer> barcodePidMap = grnEntities.stream()
				.collect(Collectors.toMap(GrnItemEntity::getBarcode, grnEntity -> Integer.valueOf(grnEntity.getPid())));
		barcodePriceRequest.setBarcodePid(barcodePidMap);
		barcodePriceRequest.setFacilityCode(purchaseOrder.getVendorId());
		log.info("[getBarcodePriceMap] Barcode price request for grnCode {} : {}",
				grnCode, barcodePriceRequest);
		List<BarcodePriceResponse> barcodePriceResponses = getBarcodePrice(barcodePriceRequest);
		Map<String, BarcodePriceResponse> barcodePriceResponsesMap = barcodePriceResponses.stream().collect(
				Collectors.toMap(BarcodePriceResponse::getBarcode, barcodePriceResponse -> barcodePriceResponse));
		log.info("[getBarcodePriceMap] Barcode price response for grnCode {} : {}",
				grnCode, barcodePriceResponses);
		return barcodePriceResponsesMap;
	}

	public void addGrnPidMaster(GRNItem grnItem, PurchaseInvoiceEntity purchaseInvoice) {
		try {
			log.info("[addGrnPidMaster] barcode {}, grnCode {}, pId {} -- po id : {}",
					grnItem.getBarcode(), grnItem.getGrnCode(), grnItem.getPid(), grnItem.getPoId());
			GrnPidMasterEntity grnPidMasterEntity = grnPidMasterEntityService.findByIdGrnCodeAndIdPid
					(grnItem.getGrnCode(), grnItem.getPid());
			log.info("[addGrnPidMaster] GrnPidMasterEntity {}", grnPidMasterEntity);
			if(grnPidMasterEntity == null){
				log.info("[addGrnPidMaster] Adding details for barcode {}, grnCode {}, pId {}",
						grnItem.getBarcode(), grnItem.getGrnCode(), grnItem.getPid());
				GRNPIDMaster grnpidMaster = getGRNPIDMaster(grnItem, purchaseInvoice);
				boolean added = grnPidDao.createGRNPIDMaster(grnpidMaster);
				if (!added)
					throw new ApplicationException("Add PID was unsuccessful",
							GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			} else{
				log.info("[addGrnPidMaster] details already existing  barcode {}, grnCode {}, pId {}",
						grnItem.getBarcode(), grnItem.getGrnCode(), grnItem.getPid());
			}
		} catch (Exception e){
			log.error("[addGrnPidMaster] barcode {}, grnItem {}, purchaseInvoice {}, error {}",
					grnItem.getBarcode(), grnItem, purchaseInvoice, e.getMessage());
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Exception to add grn pid barcode " + e.getMessage());
		}
	}

	private GRNPIDMaster getGRNPIDMaster(GRNItem grnItem, PurchaseInvoiceEntity invoice) {
		try {
			log.info("grnItem.getPoId(), Integer.valueOf(grnItem.getPid()) : {} and {}",
					grnItem.getPoId(), Integer.valueOf(grnItem.getPid()));
			PurchaseOrderItem poItem = purchaseOrderItemEntityService.
					getPurchaseOrderItemByPoNumAndPid(grnItem.getPoId(), Integer.valueOf(grnItem.getPid()));
			if (poItem != null)
				log.info("poItem : {}", poItem.toString());
			Product product = createPoProductItemsObject(poItem);

			GRNPIDMaster grnpidMaster = new GRNPIDMaster(grnItem.getGrnCode(), grnItem.getPid(),
					invoice.getVendorInvoiceNumber(), grnItem.getInvoiceRefNum(),
					grnItem.getVendorCode(), product.getBrand(), product.getCategoryId(), product.getPidDescription(),
					poItem.getVendorUnitCostPrice(),
					poItem.getPriceWithTaxes() / poItem.getQuantity(),
					poItem.getCgstRate() / poItem.getQuantity(),
					poItem.getSgstRate() / poItem.getQuantity(),
					poItem.getIgstRate() / poItem.getQuantity(),
					0l,
					new ArrayList<>(Collections.singletonList(0l)), (short) 0,
					PASSED, grnItem.getCreatedBy(), grnItem.getCreatedBy());
			return grnpidMaster;
		} catch (Exception e){
			log.error("[getGRNPIDMaster] error {}", e.getMessage());
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, e.getMessage());
		}
	}

	private Product createPoProductItemsObject(PurchaseOrderItem item) throws JsonProcessingException {
		try {
			log.info("[createPoProductItemsObject] Create Product Item : {} -- item.getProductAttributes() : {}",
					item.toString(), item.getProductAttributes());
			Product product = new Product();
			ObjectMapper mapper = new ObjectMapper();
			Map<String, Object> productAttributesMapping =
					mapper.readValue(item.getProductAttributes(), new TypeReference<Map<String, Object>>() {
					});

			product.setPid(String.valueOf(item.getProductId()));
			product.setQuantity(item.getQuantity());
			product.setPrice(item.getVendorUnitCostPrice());
			if (productAttributesMapping.get("classification") != null)
				product.setCategoryId((int) productAttributesMapping.get("classification"));
			if (productAttributesMapping.get("brand") != null)
				product.setBrand((String) productAttributesMapping.get("brand"));
			if (productAttributesMapping.get("vendor_sku") != null)
				product.setVendorSku((String) productAttributesMapping.get("vendor_sku"));
			if (productAttributesMapping.get("gtin") != null)
				product.setGtin((String) productAttributesMapping.get("gtin"));
			if (productAttributesMapping.get("upc") != null)
				product.setUpc((String) productAttributesMapping.get("upc"));
			if (productAttributesMapping.get("desc") != null)
				product.setPidDescription((String) productAttributesMapping.get("desc"));
			product.setTaxRate(item.getUgstRate());
			product.setCgstRate(item.getCgstRate());
			product.setSgstRate(item.getSgstRate());
			product.setIgstRate(item.getIgstRate());
			product.setTotalVendorCostPrice(item.getTotalVendorCostPrice());
			product.setPriceWithTaxes(item.getPriceWithTaxes());
			log.info("[createPoProductItemsObject] Product Item {}", product);
			return product;
		} catch (Exception e) {
			log.info("[createPoProductItemsObject] error {} -- stacktrace : {}", e.getMessage(), e.getStackTrace());
			throw new ApplicationException("Unable to create po product details object " + e.getMessage(),
					GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	@Scheduled(cron = "0/60 */15 * * * *")
	@SchedulerLock(name = "syncJitBarcodeToEMSTask")
	public void syncJitBarcodeToEMS() {
		log.info("Sync grn jit barcodes to EMS, schedular started {}", new Date());
		fetchBarcodeToSyncToEMS();
	}

	public void fetchBarcodeToSyncToEMS() {
		try {
			log.info("[fetchBarcodeToSyncToEMS] Sync unsynced grn jit barcodes to EMS {}", new Date());
			List<Tuple> barcodeTupleList = grnItemMetaDataEntityService.findUnsyncedBarcodeInEMS(jitBarcodeEmsSyncRetry);
			List<String> barcodeList = new ArrayList<>();
			for (Tuple tuple : barcodeTupleList) {
				String barcode = tuple.get("barcode", String.class);
				barcodeList.add(barcode);
			}
			log.info("[fetchBarcodeToSyncToEMS] barcodeList {}", barcodeList);
			Map<String, List<String>> result = createRequestAndPushDataToKafka(barcodeList);
			log.info("[fetchBarcodeToSyncToEMS] barcodeList {}, time {}, result {}", barcodeList, new Date(), result);
		} catch (Exception e){
			log.error("[fetchBarcodeToSyncToEMS] Error in grn ems sync barcode scheduler {}, error {}",
					new Date(), e.getMessage());
		}
	}

	public Map<String, List<String>> createRequestAndPushDataToKafka(List<String> barcodeList) {
		try {
			log.info("[createRequestAndPushDataToKafka] barcodeList {}", barcodeList);
			List<GrnItemEntity> grnItemList= grnItemEntityService.findByBarcodeIn(barcodeList);
			List<GrnItemMetaDataEntity> grnItemMetaDataEntities =
					grnItemMetaDataEntityService.findByBarcodeIn(barcodeList);
			Map<String, List<String>> successStatusAndBarcodeListMapping = new HashMap<>();

			Map<String, List<GrnItemMetaDataEntity>> barcodeAndGrnItemMetaDataMapping = new HashMap<>();
			for(GrnItemMetaDataEntity metaData: grnItemMetaDataEntities){
				List<GrnItemMetaDataEntity> metaDataEntityList = new ArrayList<>();
				if(barcodeAndGrnItemMetaDataMapping.containsKey(metaData.getBarcode())){
					metaDataEntityList = barcodeAndGrnItemMetaDataMapping.get(metaData.getBarcode());
				}
				metaDataEntityList.add(metaData);
				barcodeAndGrnItemMetaDataMapping.put(metaData.getBarcode(), metaDataEntityList);
			}

			for(GrnItemEntity item : grnItemList){
				log.info("Syncing for barcode {}, grn code {}, item {}", item.getBarcode(), item.getGrnCode(), item);
				GRNScanItemRequest grnScanItemRequest = createGrnScanItemRequest(item,
						barcodeAndGrnItemMetaDataMapping.get(item.getBarcode()));
				List<GrnItemMetaDataEntity> metaDataList =  barcodeAndGrnItemMetaDataMapping.get(item.getBarcode());
				String count = "0";
				for(GrnItemMetaDataEntity metaData: metaDataList){
					if(metaData.getKey().equalsIgnoreCase(EMS_SYNCED_RETRY_COUNT))
						count = String.valueOf(Integer.parseInt(metaData.getValue()) + 1);
				}
				log.info("Syncing for barcode {}, grn code {}, count {}", item.getBarcode(), item.getGrnCode(), count);
				try {
					pushMessageToEmsKafka(grnScanItemRequest);
					log.info("Item successfully synced {}, grn code {}", item.getBarcode(), item.getGrnCode());
					List<String> successBarcodeList = new ArrayList<>();
					if(successStatusAndBarcodeListMapping.containsKey("SUCCESS")){
						successBarcodeList = successStatusAndBarcodeListMapping.get("SUCCESS");
					}
					successBarcodeList.add(item.getBarcode());
					successStatusAndBarcodeListMapping.put("SUCCESS", successBarcodeList);
					log.info("Item successfully synced {}, grn code {}", item.getBarcode(), item.getGrnCode());
				} catch (Exception e){
					log.error("Unable to sync barcode details to EMS {}, count {}: error {}",
							item.getBarcode(), count, e.getMessage());
					saveGrnItemMetaData(grnScanItemRequest.getBarcode(), EMS_SYNCED_RETRY_COUNT,
							count, grnScanItemRequest.getCreatedBy());
					log.info("Unable to sync barcode {}, grn code {}", item.getBarcode(), item.getGrnCode());
					List<String> failureBarcodeList = new ArrayList<>();
					if(successStatusAndBarcodeListMapping.containsKey("FAILURE")){
						failureBarcodeList = successStatusAndBarcodeListMapping.get("FAILURE");
					}
					failureBarcodeList.add(item.getBarcode());
					successStatusAndBarcodeListMapping.put("FAILURE", failureBarcodeList);
					log.info("Unable to sync barcode {}, grn code {}", item.getBarcode(), item.getGrnCode());
				}
			}
			return successStatusAndBarcodeListMapping;
		} catch (Exception e) {
			log.error("[createRequestAndPushDataToKafka] " +
					"Error in grn ems sync barcode scheduler {}", e.getMessage());
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, e.getMessage());
		}
	}

	private GRNScanItemRequest createGrnScanItemRequest(GrnItemEntity item,
														List<GrnItemMetaDataEntity> metaDataEntities) {
		log.info("[createGrnScanItemRequest]");
		String fittingId = "", uwItemId = "";
		for(GrnItemMetaDataEntity metaData: metaDataEntities){
			if(metaData.getKey().equalsIgnoreCase(UW_ITEM_ID_META_DATA))
				uwItemId = metaData.getValue();
			if(metaData.getKey().equalsIgnoreCase(FITTING_ID_META_DATA))
				fittingId = metaData.getValue();
		}
		GRNScanItemRequest request = new GRNScanItemRequest();
		request.setBarcode(item.getBarcode());
		request.setFittingId(fittingId);
		request.setUwItemId(uwItemId);
		request.setGrnCode(item.getGrnCode());
		request.setProductId(item.getPid());
		request.setFacilityCode(item.getFacility());
		request.setQcStatus(item.getQcStatus());
		return request;
	}

	public Map<String, List<String>> syncBarcodeInEMS(List<String> barcodeList, String emsSyncStatus) {
		try {
			log.info("[syncBarcodeInEMS] barcodeList {}, emsSyncStatus {}", barcodeList, emsSyncStatus);
			if(barcodeList != null && !barcodeList.isEmpty()){
				log.info("[unsyncedBarcodeList] Barcode list {}", barcodeList);
				Map<String, List<String>> result = createRequestAndPushDataToKafka(barcodeList);
				log.info("[syncBarcodeInEMS] barcodeList {}, result {}", barcodeList, result);
				return result;
			} else {
				List<Tuple> barcodeTupleList = grnItemMetaDataEntityService.findUnsyncedBarcodeInEMS(jitBarcodeEmsSyncRetry);
				List<String> unsyncedBarcodeList = new ArrayList<>();
				for (Tuple tuple : barcodeTupleList) {
					String barcode = tuple.get("barcode", String.class);
					unsyncedBarcodeList.add(barcode);
				}
				log.info("[unsyncedBarcodeList] Barcode list with 0 synced status {}", unsyncedBarcodeList);
				Map<String, List<String>> result = createRequestAndPushDataToKafka(barcodeList);
				log.info("[syncBarcodeInEMS] Barcode list with 0 synced status {}, result {}", barcodeList, result);
				return result;
			}
		} catch (Exception e) {
			log.error("[syncBarcodeInEMS] Error in sync barcode {}", e.getMessage());
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, e.getMessage());
		}
	}

	public List<BarcodePriceResponse> getBarcodePrice(BarcodePriceRequest barcodePriceRequest) throws Exception {
		log.info("[getBarcodePrice] Request received to fetch landing cost : {}", barcodePriceRequest);
		if (StringUtils.isBlank(barcodePriceRequest.getFacilityCode())) {
			throw new CustomException("Facility code is mandatory", HttpStatus.BAD_REQUEST.value());
		}
		List<String> barcodeList = getBarcodeList(barcodePriceRequest);
		String facilityCode = barcodePriceRequest.getFacilityCode();
		List<BarcodePriceResponse> barcodePriceResponseList = new ArrayList<>();
		log.info("[getBarcodePrice] Fetching price from barcodes : {}", barcodeList);
		FacilityDetailsResponse facilityDetailsResponse = facilityDetailsUtils.getFacilityDetails(facilityCode);
		if (facilityDetailsResponse == null || facilityDetailsResponse.getFacilityDetails() == null) {
			log.error("[getBarcodePrice] Unable to fetch facility details : {}", facilityCode);
			throw new CustomException("Incorrect facility code is passed " + facilityCode, HttpStatus.BAD_REQUEST.value());
		}
		LegalOwner facilityLegalOwnerDetails = legalOwnerConfigRepository
				.findByLegalOwner(facilityDetailsResponse.getFacilityDetails().getLegalOwner());
		Set<String> uniquePids = new HashSet<String>();
		TaxInvoiceDetailRequestModel createTaxInvoiceRequest = getTaxInvoiceDetailRequestModel(facilityDetailsResponse,
				facilityDetailsResponse, facilityLegalOwnerDetails, facilityLegalOwnerDetails);
		Map<String, Integer> barcodePid = getBarcodePidMap(barcodePriceRequest);
		if (!barcodeList.isEmpty()) {
			getPriceFromGrn(barcodeList, barcodePid, facilityCode, facilityLegalOwnerDetails.getLegalOwner(),
					barcodePriceResponseList, facilityDetailsResponse, facilityDetailsResponse, createTaxInvoiceRequest, uniquePids);
			log.info("[getBarcodePrice] Get barcode detail remaining list after fetching price from GRN {} : {}",
					barcodePriceRequest, barcodeList);
		}
		if (!barcodeList.isEmpty()) {
			log.info("[getBarcodePrice] Fetching barcode price from IMS : {}", barcodeList);
			getPriceFromIMS(barcodeList, barcodePid, createTaxInvoiceRequest, facilityCode,
					facilityLegalOwnerDetails.getLegalOwner(), barcodePriceResponseList, uniquePids);
			log.info("[getBarcodePrice] Get barcode detail remaining list after fetching price from IMS {} : {}",
					barcodePriceRequest, barcodeList);
		}
		if (!barcodeList.isEmpty()) {
			log.info("[getBarcodePrice] Fetching barcode price from Purchase Ledger : {}", barcodeList);
			getPriceFromPurchaseLedger(barcodeList, barcodePid, facilityCode, facilityLegalOwnerDetails.getLegalOwner(),
					barcodePriceResponseList, facilityDetailsResponse, facilityDetailsResponse, createTaxInvoiceRequest, uniquePids);
			log.info("[getBarcodePrice] Get barcode detail remaining list after fetching price from Purchase Ledger {} : {}",
					barcodePriceRequest, barcodeList);
		}
		if (!barcodeList.isEmpty() && (facilityCodeList == null || facilityCodeList.isEmpty()
				|| facilityCodeList.contains(facilityCode))) {
			log.info("[getBarcodePrice] Fetching barcode price from transfer : {}", barcodeList);
			getPriceFromInventoryTransfer(barcodeList, barcodePid, createTaxInvoiceRequest, facilityCode,
					barcodePriceResponseList, uniquePids);
			log.info("[getBarcodePrice] Get barcode detail remaining list after fetching price from TRANSFER {} : {}",
					barcodePriceRequest, barcodeList);
		}
		updateBarcodePriceInResponse(barcodePriceResponseList, createTaxInvoiceRequest,
				facilityLegalOwnerDetails.getOperatingCurrency());
		updateMissingBarcodesInResponse(barcodeList, barcodePid, barcodePriceResponseList);
		log.info("[getBarcodePrice] Fetching price from barcodes {} : {}", barcodeList, barcodePriceResponseList);
		return barcodePriceResponseList;
	}

	private Map<String, Integer> getBarcodePidMap(BarcodePriceRequest barcodePriceRequest) {
		if (barcodePriceRequest.getBarcodePid() == null) {
			return new HashMap<>();
		}
		return barcodePriceRequest.getBarcodePid();
	}

	private void updateMissingBarcodesInResponse(List<String> barcodeList, Map<String, Integer> barcodePid,
			List<BarcodePriceResponse> barcodePriceResponseList) {
		for (String barcode : barcodeList) {
			String pid = barcodePid.get(barcode) != null ? String.valueOf(barcodePid.get(barcode)) : null;
			BarcodePriceResponse barcodePriceResponse = createBarcodePriceResponse(barcode, null, pid);
			barcodePriceResponseList.add(barcodePriceResponse);
		}
	}

	private List<String> getBarcodeList(BarcodePriceRequest barcodePriceRequest) throws CustomException {
		List<String> barcodeList = new ArrayList<String>();
		if (barcodePriceRequest.getBarcodeList() != null && !barcodePriceRequest.getBarcodeList().isEmpty()) {
			barcodeList.addAll(barcodePriceRequest.getBarcodeList());
		} else if (barcodePriceRequest.getBarcodePid() != null && !barcodePriceRequest.getBarcodePid().isEmpty()) {
			barcodeList.addAll(barcodePriceRequest.getBarcodePid().keySet());
		}
		if (barcodeList.isEmpty()) {
			log.error("Barcode list empty");
			throw new CustomException("Empty barcode list", HttpStatus.BAD_REQUEST.value());
		}
		return barcodeList;
	}

	private void getPriceFromIMS(List<String> barcodeList, Map<String, Integer> barcodePid,
			TaxInvoiceDetailRequestModel createTaxInvoiceRequest, String facilityCode, String facilityLegalOwner,
			List<BarcodePriceResponse> barcodePriceResponseList, Set<String> uniquePids) {
		try {
			BarcodePriceDetailsRequest barcodePriceDetailsRequest = getBarcodePriceDetailsRequest(barcodeList,
					facilityCode, facilityLegalOwner);
			log.info("[getPriceFromIMS] Barcode price request for {} : {}", barcodeList, barcodePriceDetailsRequest);
			FetchBarcodeInwardResponse fetchBarcodeInwardResponse = imsService
					.fetchBarcodePriceDetails(barcodePriceDetailsRequest);
			log.info("[getPriceFromIMS] Barcode price response for {} : {}", barcodeList, fetchBarcodeInwardResponse);
			if (fetchBarcodeInwardResponse != null
					&& fetchBarcodeInwardResponse.getBarcodeInwardResponseList() != null) {
				for (BarcodeInwardResponse barcodeInwardResponse : fetchBarcodeInwardResponse
						.getBarcodeInwardResponseList()) {
					updateBarcodePriceAndTaxDetailsRequest(barcodeList, barcodePid, barcodeInwardResponse.getBarcode(),
							barcodeInwardResponse.getLandedCostWithoutTax().doubleValue(),
							String.valueOf(barcodeInwardResponse.getPid()), barcodePriceResponseList, uniquePids,
							createTaxInvoiceRequest);
				}
			}
		} catch (Exception ex) {
			log.error("[getPriceFromIMS] Exception fetching barcode price for {}", barcodeList, ex);
		}
	}

	private BarcodePriceDetailsRequest getBarcodePriceDetailsRequest(List<String> barcodeList, String facilityCode,
			String legalOwner) {
		BarcodePriceDetailsRequest barcodePriceDetailsRequest = new BarcodePriceDetailsRequest();
		barcodePriceDetailsRequest.setBarcodes(barcodeList);
		barcodePriceDetailsRequest.setFacility(facilityCode);
		barcodePriceDetailsRequest.setLegalOwner(legalOwner);
		return barcodePriceDetailsRequest;
	}

	private void getPriceFromInventoryTransfer(List<String> barcodeList, Map<String, Integer> barcodePid,
			TaxInvoiceDetailRequestModel createTaxInvoiceRequest, String facilityCode,
			List<BarcodePriceResponse> barcodePriceResponseList, Set<String> uniquePids) throws Exception {
		try {
			List<BarcodeAndCostPriceDto> barcodeAndCostPriceDtoList = inventoryTransferClient.getCostPrice(barcodeList,
					facilityCode);
			if (barcodeAndCostPriceDtoList != null && !CollectionUtils.isEmpty(barcodeAndCostPriceDtoList)) {
				for (BarcodeAndCostPriceDto barcodeAndCostPriceDto : barcodeAndCostPriceDtoList) {
					String barcode = barcodeAndCostPriceDto.getBarcode();
					double unitPrice = barcodeAndCostPriceDto.getCostPrice();
					String pid = barcodeAndCostPriceDto.getPid();
					updateBarcodePriceAndTaxDetailsRequest(barcodeList, barcodePid, barcode, unitPrice, pid,
							barcodePriceResponseList, uniquePids, createTaxInvoiceRequest);
				}
			}
		} catch (Exception ex) {
			log.error("[getPriceFromInventory] Excetion caught for barcodes {} and facility {}", barcodeList,
					facilityCode, ex);
		}
		log.info("[getPriceFromInventory] Landing cost post transfer {}", barcodePriceResponseList);
	}

	private void getPriceFromGrn(List<String> barcodeList, Map<String, Integer> barcodePid, String facilityCode,
			String facilityLegalOwner, List<BarcodePriceResponse> barcodePriceResponseList,
			FacilityDetailsResponse sourceFacilityResponse, FacilityDetailsResponse destinationFacilityResponse,
			TaxInvoiceDetailRequestModel createTaxInvoiceRequest, Set<String> uniquePids) {
		try {
			List<Tuple> grnItemList = grnItemEntityService.findPriceByBarcodesInAndFacilityOrLegalOwner(barcodeList,
					facilityCode, facilityLegalOwner);
			log.info("[getPriceFromGrn] barcodeList {}, grnItemList {}, facility {}", barcodeList, grnItemList,
					facilityCode);
			updateBarcodeDetailsForGRN(barcodeList, barcodePid, grnItemList, barcodePriceResponseList,
					createTaxInvoiceRequest, uniquePids);
			log.info("[getPriceFromGrn] barcodePriceResponseList : {}", barcodePriceResponseList);
			if (!barcodeList.isEmpty()) {
				List<Tuple> grnItemsArchivedList = grnItemsArchivedEntityService
						.findBarcodeListItemPriceByFacility(barcodeList, facilityCode);
				log.info("[getPriceFromGrn] barcodeList {}, grnItemsArchivedList {}, facility {}", barcodeList,
						grnItemsArchivedList, facilityCode);
				updateBarcodeDetailsForGRN(barcodeList, barcodePid, grnItemsArchivedList, barcodePriceResponseList,
						createTaxInvoiceRequest, uniquePids);
				log.info("[getPriceFromGrnArchiva] barcodePriceResponseList : {}", barcodePriceResponseList);
			}
		} catch (Exception ex) {
			log.error("[getPriceFromGrn] Excetion caught for barcodes {} and facility {}", barcodeList, facilityCode,
					ex);
		}
		log.info("[getPriceFromGrn] Landing cost post GRN {}", barcodePriceResponseList);
	}

	private void getPriceFromPurchaseLedger(List<String> barcodeList, Map<String, Integer> barcodePid,
			String facilityCode, String facilityLegalOwner, List<BarcodePriceResponse> barcodePriceResponseList,
			FacilityDetailsResponse sourceFacilityResponse, FacilityDetailsResponse destinationFacilityResponse,
			TaxInvoiceDetailRequestModel createTaxInvoiceRequest, Set<String> uniquePids) {
		try {
			log.info("[getPriceFromPurchaseLedger] fetching price for barcodeList {}, facilityLegalOwner {}", barcodeList,
					facilityLegalOwner);
			List<AccountBarcodePurchasePricing> barcodeDetails = accountBarcodePurchasePricingRepo.getBarcodePriceFromAccountBarcodePurchasePricingEntity(barcodeList, facilityLegalOwner);
			log.info("[getPriceFromPurchaseLedger] barcodeList {}, barcodeDetails {}, facility {}", barcodeList,
					barcodeDetails, facilityCode);
			updateBarcodeDetailsForPurchaseLedger(barcodeList, barcodePid, barcodeDetails, barcodePriceResponseList,
					createTaxInvoiceRequest, uniquePids);
			log.info("[getPriceFromPurchaseLedger] barcodePriceResponseList : {}", barcodePriceResponseList);
		} catch (Exception ex) {
			log.error("[getPriceFromPurchaseLedger] Excetion caught for barcodes {} and facility {}", barcodeList,
					facilityCode, ex);
		}
		log.info("[getPriceFromPurchaseLedger] Landing cost post purchase ledger {}", barcodePriceResponseList);
	}

	private void updateBarcodeDetailsForGRN(List<String> barcodeList, Map<String, Integer> barcodePid,
			List<Tuple> grnItemList, List<BarcodePriceResponse> barcodePriceResponseList,
			TaxInvoiceDetailRequestModel createTaxInvoiceRequest, Set<String> uniquePids) throws Exception {
		for (Tuple barcodeDetails : grnItemList) {
			String barcode = barcodeDetails.get("barcode", String.class);
			double unitPrice = Double.parseDouble(barcodeDetails.get("cost_price").toString());
			String pid = barcodeDetails.get("pid", String.class);
			updateBarcodePriceAndTaxDetailsRequest(barcodeList, barcodePid, barcode, unitPrice, pid,
					barcodePriceResponseList, uniquePids, createTaxInvoiceRequest);
		}
		log.info("barcodeList after removing grn barcode is {} ", barcodeList);
	}

	private void updateBarcodeDetailsForPurchaseLedger(List<String> barcodeList, Map<String, Integer> barcodePid,
			List<AccountBarcodePurchasePricing> barcodeDetails,
			List<BarcodePriceResponse> barcodePriceResponseList, TaxInvoiceDetailRequestModel createTaxInvoiceRequest,
			Set<String> uniquePids) throws Exception {
		for (AccountBarcodePurchasePricing barcodeDetail : barcodeDetails) {
			String barcode = barcodeDetail.getBarcode();
			if (barcodeList.contains(barcode) && (barcodePid.isEmpty() || (barcodePid.containsKey(barcode)
					&& barcodeDetail.getProductId().equals(barcodePid.get(barcode))))) {
				double unitPrice = barcodeDetail.getUnitPriceWithoutTax().doubleValue();
				String pid = String.valueOf(barcodeDetail.getProductId());
				updateBarcodePriceAndTaxDetailsRequest(barcodeList, barcodePid, barcode, unitPrice, pid,
						barcodePriceResponseList, uniquePids, createTaxInvoiceRequest);
			}
		}
		log.info("barcodeList after removing purchase ledger barcode is {} ", barcodeList);
	}

	private boolean barcodePidMatch(Map<String, Integer> barcodePid, String barcode, String pid) {
		if (!barcodePid.containsKey(barcode)
				|| StringUtils.equalsIgnoreCase(pid, String.valueOf(barcodePid.get(barcode)))) {
			return true;
		}
		return false;
	}

	private void updateBarcodePriceAndTaxDetailsRequest(List<String> barcodeList, Map<String, Integer> barcodePid,
			String barcode, double unitPrice, String pid, List<BarcodePriceResponse> barcodePriceResponseList,
			Set<String> uniquePids, TaxInvoiceDetailRequestModel createTaxInvoiceRequest) throws Exception {
		if (barcodePidMatch(barcodePid, barcode, pid)) {
			BarcodePriceResponse barcodePriceResponse = createBarcodePriceResponse(barcode, unitPrice, pid);
			updateProductDetail(createTaxInvoiceRequest, unitPrice, pid, uniquePids);
			barcodePriceResponseList.add(barcodePriceResponse);
			barcodeList.remove(barcode);
		}
	}

	private BarcodePriceResponse createBarcodePriceResponse(String barcode, Double unitPrice, String pid) {
		BarcodePriceResponse barcodePriceResponse = new BarcodePriceResponse();
		barcodePriceResponse.setBarcode(barcode);
		if (unitPrice != null && unitPrice > 0) {
			unitPrice = Math.round(unitPrice * 10000.0) / 10000.0;
		}
		barcodePriceResponse.setPrice(unitPrice);
		barcodePriceResponse.setPid(pid);
		return barcodePriceResponse;
	}

	private void updateProductDetail(TaxInvoiceDetailRequestModel createTaxInvoiceRequest, double unitPrice, String pid,
			Set<String> uniquePids) {
		if (!uniquePids.contains(pid)) {
			ProductDetailModel productDetailModel = updateProductDetailModels(unitPrice, pid);
			createTaxInvoiceRequest.getProductDetails().add(productDetailModel);
			log.info("[updateProductDetail] productDetail added in product taxInvoice request {} , {} ",
					productDetailModel, createTaxInvoiceRequest);
			uniquePids.add(pid);
		}
	}

	@SuppressWarnings("unchecked")
	public JSONObject fetchItemBarcode(FetchItemRequestModel fetchItemRequestModel, String facilityCode)
			throws Exception {
		try {
			log.info("[fetchItemBarcode] Fetch barcode {}, {}", fetchItemRequestModel.getItemCode(), facilityCode);
			CustomRequest request = new CustomRequest();
			Map<String, Object> payload = new HashMap<>();
			payload.put("itemCode", fetchItemRequestModel.getItemCode());
			request.setPayload(payload);
			request.setFacility(facilityCode);
			CustomResponse customResponse = inventoryAdapterClient.searchItemByBarcode(request);
			log.info("[fetchItemBarcode] Fetch Item Barcode Response from unicom is {}, {}",
					fetchItemRequestModel.getItemCode(), customResponse);
			JSONObject responseObj= new JSONObject(customResponse.getResponse());
			log.info("[fetchItemBarcode] Fetch Item Barcode Response from unicom is {}, {}",
					fetchItemRequestModel.getItemCode(), responseObj);
			Boolean success = (Boolean) responseObj.get("successful");
			if (!success) {
				Object errorObject = responseObj.getJSONArray("errors").getJSONObject(0);
				log.error("[fetchItemBarcode] Error Fetch Item Barcode {}, {}", fetchItemRequestModel.getItemCode(), errorObject);
				Map<String, Object> errorMap = objectMapper.readValue(errorObject.toString(), Map.class);
				throw new Exception(errorMap.get("message") + " : " + errorMap.get("description"));
			} else {
				JSONObject itemDtoObj = responseObj.getJSONObject("itemDTO");
				log.info("[fetchItemBarcode] barcode {}, itemDtoObj {}",
						fetchItemRequestModel.getItemCode(), itemDtoObj);
				return itemDtoObj;
			}
		} catch (Exception e) {
			log.error("Exception occurred while fetching barcode in unicomAdapter" +
					" for {}, {} : {}", fetchItemRequestModel, facilityCode, e.getMessage());
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Exception "+ e.getMessage());
		}
	}

	public List<GrnItemBoxBarcodePidMapping> getBoxCodeAndProductId(String grnCode) {
		try{
			log.info("[getBoxCodeAndProductId] grnCode {}", grnCode);
			List<GrnItemBoxBarcodePidMapping> grnItemBoxBarcodePidMappingList = new ArrayList<>();
			List<Tuple> boxBarcodeAndPidList = grnItemEntityService.getBoxBarcodeAndPid(grnCode);
			log.info("[getBoxCodeAndProductId] grnCode {}, boxBarcodeAndPidList {}",
					grnCode, boxBarcodeAndPidList);
			for (Tuple tuple : boxBarcodeAndPidList) {
				GrnItemBoxBarcodePidMapping grnItemBoxBarcodePidMapping = new GrnItemBoxBarcodePidMapping();
				grnItemBoxBarcodePidMapping.setPid(Integer.parseInt(tuple.get("pid", String.class)));
				grnItemBoxBarcodePidMapping.setQcPassBoxBarcode(tuple.get("qc_pass_box_barcode", String.class));
				grnItemBoxBarcodePidMapping.setInvoiceRefNum(tuple.get("invoice_ref_num", String.class));
				grnItemBoxBarcodePidMapping.setTotalPidCount(Integer.parseInt(tuple.get("total_pid_count").toString()));
				grnItemBoxBarcodePidMappingList.add(grnItemBoxBarcodePidMapping);
			}
			log.info("[getBoxCodeAndProductId] grnCode {}, grnItemBoxBarcodePidMappingList {}",
					grnCode, grnItemBoxBarcodePidMappingList);
			return grnItemBoxBarcodePidMappingList;
		} catch (Exception e){
			log.error("Unable to fetch box barcode and pid details {}", e.getMessage());
			throw new ApplicationException("Exception : " + e.getMessage(),
					GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	public List<BarcodeExpiryDateResponse> getBarcodeExpiryDate(List<String> barcodeList, List<String> barcodeReqList) {
		try {
			log.info("[getBarcodeExpiryDate] barcodeList {}, barcodeReqList {}", barcodeList, barcodeReqList);
			if(barcodeList == null || barcodeList.isEmpty())
				barcodeList = barcodeReqList;
			log.info("[getBarcodeExpiryDate] barcodeList {}", barcodeList);
			if(barcodeList.size() > grnConfig.getBarcodeExpiryDateMaxAllowedLimit())
				throw new ApplicationException("Barcode list size can not be greater than " + grnConfig.getBarcodeExpiryDateMaxAllowedLimit(),
						GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
			List<GrnItemEntity> barcodeDetails = getBarcodeDetailsFromGrnItems(barcodeList);
			Map<String, GrnItemEntity> barcodeExpiryDateMapping = mapBarcodeDetails(barcodeDetails);
			return fetchBarcodeExpiryDetails(barcodeList, barcodeExpiryDateMapping);
		} catch (Exception e) {
			log.error("[getBarcodeExpiryDate] Error {}", e.getMessage());
			throw new ApplicationException("Exception : " + e.getMessage(),
					GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	private List<BarcodeExpiryDateResponse> fetchBarcodeExpiryDetails(List<String> barcodeList, Map<String, GrnItemEntity> barcodeExpiryDateMapping) {
		try {
			log.info("[fetchBarcodeExpiryDetails] barcodeList {}", barcodeList);
			List<BarcodeExpiryDateResponse> barcodeExpiryDateResponses = new ArrayList<>();
			for (String barcode : barcodeList) {
				BarcodeExpiryDateResponse response = new BarcodeExpiryDateResponse();
				response.setBarcode(barcode);
				try {
					Date expiryDate = null;
					if (barcodeExpiryDateMapping.containsKey(barcode)) {
						GrnItemEntity item = barcodeExpiryDateMapping.get(barcode);
						if (item.getExpiryDate() != null) {
							expiryDate = item.getExpiryDate();
							log.info("[fetchBarcodeExpiryDetails] Expiry date from GRN item {}, expiryDate {}",
									barcode, expiryDate);
							response.setSuccess(true);
						} else {
							if(grnConfig.isUnicomCallEnabled()){
								log.info("[fetchBarcodeExpiryDetails] Expiry date from does not exist in GRN item {}",
										barcode);
								GrnMasterEntity grnMaster = grnMasterEntityService.findByGrnCode(item.getGrnCode());
								expiryDate = getExpiryDateFromUnicom(item.getBarcode(), grnMaster.getUnicomGrnCode(),
										item.getFacility(), item.getPid());
								log.info("[fetchBarcodeExpiryDetails] Expiry date from unicom {}, expiryDate {}",
										barcode, expiryDate);
								response.setSuccess(expiryDate != null);
							} else {
								log.info("[fetchBarcodeExpiryDetails] Barcode Unicom call blocked, not fetching expiry date from unicom {}", barcode);
								response.setSuccess(false);
							}
						}
					} else {
						if(grnConfig.isUnicomCallEnabled()){
							log.info("[fetchBarcodeExpiryDetails] Barcode does not exist in grn {}", barcode);
							JSONObject barcodeDetail = fetchGrnDetailsFromUnicom(barcode);
							expiryDate = getExpiryDateFromUnicom(barcode, barcodeDetail.getString("inflowReceiptCode"),
									barcodeDetail.getString("createdFacilityCode"), barcodeDetail.getString("itemSKU"));
							response.setSuccess(true);
						} else {
							log.info("[fetchBarcodeExpiryDetails] Unicom call blocked, not fetching expiry date from unicom {}", barcode);
							response.setSuccess(false);
						}
					}
					log.info("[fetchBarcodeExpiryDetails] barcode {}, expiryDate {}", barcode, expiryDate);
					response.setExpiryDate(expiryDate);
				} catch (Exception ex) {
					log.info("[fetchBarcodeExpiryDetails] Error barcode {}, error {}", barcode, ex.getMessage());
					response.setSuccess(false);
					response.setErrorMessage(ex.getMessage());
				}
				log.info("[fetchBarcodeExpiryDetails] barcode {}, response {}", barcode, response);
				barcodeExpiryDateResponses.add(response);
			}
			return barcodeExpiryDateResponses;
		} catch (Exception e){
			log.error("[fetchBarcodeExpiryDetails] Error {}", e.getMessage());
			throw new ApplicationException("Exception : " + e.getMessage(),
					GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	private JSONObject fetchGrnDetailsFromUnicom(String barcode) {
		try{
			log.info("[fetchGrnDetailsFromUnicom] barcode {}", barcode);
			FetchItemRequestModel model = new FetchItemRequestModel();
			model.setItemCode(barcode);
			String facilityCode = grnConfig.getUnicomFetchBarcodeFacility();
			JSONObject barcodeDetail = fetchItemBarcode(model, facilityCode);
			log.info("[fetchGrnDetailsFromUnicom]  barcode {}, barcodeDetail {}", barcode, barcodeDetail);
			return barcodeDetail;
		} catch (Exception e){
			log.error("[fetchGrnDetailsFromUnicom] Error {}", e.getMessage());
			throw new ApplicationException("Exception : " + e.getMessage(),
					GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	private List<GrnItemEntity> getBarcodeDetailsFromGrnItems(List<String> barcodeList) {
		try {
			List<GrnItemEntity> barcodeRes = grnItemService.findByBarcodeIn(barcodeList);
			log.info("barcodeRes : {}", barcodeRes);
			List<GrnItemsArchivedEntity> archivedBarcodeRes = new ArrayList<>();
			List<String> archivedBarcodeList = null;
			if (barcodeRes == null || barcodeRes.isEmpty()) {
				archivedBarcodeList = barcodeList;
				log.info("barcodeRes is either null or empty");
			} else if (barcodeList.size() > barcodeRes.size()) {
				archivedBarcodeList = barcodeRes.stream().filter(e -> !barcodeList.contains(e.getBarcode()))
						.map(e -> e.getBarcode()).collect(Collectors.toList());
				log.info("archivedBarcodeList : {}", archivedBarcodeList);
			}
			if (archivedBarcodeList != null && !archivedBarcodeList.isEmpty()) {
				archivedBarcodeRes = grnItemsArchivedEntityService.findByBarcodeIn(archivedBarcodeList);
				log.info("archivedBarcodeRes if empty or null : {}", archivedBarcodeRes);
				convertToGrnItemEntity(barcodeRes, archivedBarcodeRes);
			}
			log.info("barcodeRes final : {}", barcodeRes);
			return barcodeRes;
		} catch (Exception e) {
			log.error("[getBarcodeDetailsFromGrnItems] Error {}", e.getMessage());
			throw new ApplicationException("SQL Exception " + e.getMessage(),
					GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	private void convertToGrnItemEntity(List<GrnItemEntity> barcodeRes, List<GrnItemsArchivedEntity> archivedBarcodeRes) {
		if (archivedBarcodeRes != null && !archivedBarcodeRes.isEmpty()){
			archivedBarcodeRes.stream().forEach(e -> {
				barcodeRes.add(objectMapper.convertValue(e, GrnItemEntity.class));
			});
		}
	}

	private Map<String, GrnItemEntity> mapBarcodeDetails(List<GrnItemEntity> barcodeDetails) {
		try {
			log.info("[mapBarcodeDetails]");
			Map<String, GrnItemEntity> barcodeExpiryDateMapping = new HashMap<>();
			for (GrnItemEntity item : barcodeDetails) {
				barcodeExpiryDateMapping.put(item.getBarcode(), item);
			}
			log.info("[mapBarcodeDetails] barcodeExpiryDateMapping {}", barcodeExpiryDateMapping);
			return barcodeExpiryDateMapping;
		} catch (Exception e) {
			log.error("[mapBarcodeDetails] Error {}", e.getMessage());
			throw new ApplicationException("SQL Exception " + e.getMessage(),
					GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	private Date getExpiryDateFromUnicom(String barcode, String grnCode, String facilityCode, String pId) {
		try {
			log.info("[getExpiryDateFromUnicom] barcode {}, grnCode {}, facilityCode {}, pId {}",
					barcode, grnCode, facilityCode, pId);
			Date expiry = null;
			String key = grnCode + "_"+ pId;
			if (RedisHandler.hasKey(key)) {
				log.info("[getExpiryDateFromUnicom] Expiry date exist in redis barcode {}, key {}", barcode, key);
				String epochTimeStr = (String) RedisHandler.redisOps(RedisOps.GET, key);
				long epochTime = Long.parseLong(epochTimeStr);
				expiry = new Date(epochTime);
				log.info("[getExpiryDateFromUnicom] Expiry date exist in redis barcode {}, key {}, expiry {}",
						barcode, key, expiry);
			} else {
				log.info("[getExpiryDateFromUnicom] Expiry date does not exist in redis barcode {}, key {}",
						barcode, key);
				GrnResponse grnResponse = unicomConnector.getUnicomeGrn(facilityCode, grnCode);
				log.info("[getExpiryDateFromUnicom grnResponse for barcode {} expiry {}", barcode, grnResponse);
				Optional<Date> optionalExpiry = grnResponse.getInflowReceipt().getInflowReceiptItems().stream()
						.filter(t -> t.getItemSKU().equals(pId.toString()))
						.map(GrnResponse.GrnItemResponse::getExpiry).findFirst();
				if (optionalExpiry.isPresent()) {
					expiry = optionalExpiry.get();
					log.info("[getExpiryDateFromUnicom] OptionalExpiry barcode {}, date is {}",
							barcode, optionalExpiry.get());
					RedisHandler.redisOps(RedisOps.SETVALUETTL,key, expiry, 48L, TimeUnit.HOURS);
				}
			}
			log.info("[getExpiryDateFromUnicom] barcode {} expiry date {}", barcode, expiry);
			return expiry;
		} catch (Exception e) {
			log.error("[transferBarcodeExpiry] Error while getting expiry date" + e);
			throw new ApplicationException("Exception from unicom " + e.getMessage(),
					GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	public void createGrnItems(AutoGrnScanDetails request, PurchaseInvoiceEntity purchaseInvoice) throws ApplicationException {
		try {
			String vendorInvoiceNumber = purchaseInvoice.getVendorInvoiceNumber();
			log.info("[createGrnItems] request grnCode {}, vendorInvoiceNumber {}, purchaseInvoice {}",
					request.getGrnCode(), vendorInvoiceNumber, purchaseInvoice);
			PurchaseOrder purchaseOrder = purchaseOrderEntityServiceImpl.getPurchaseOrder(request.getPoNum());
			for (AutoGrnItemScanDetails itemScanDetails: request.getGrnItemDetailList()) {
				try {
					createGrnItem(request, itemScanDetails, purchaseInvoice, purchaseOrder);
					itemScanDetails.setSuccess(true);
					itemScanDetails.setError(null);
				} catch (Exception e) {
					log.error("[createGrnItems] request grnCode {}, barcode {}, vendorInvoiceNumber {}, " +
									"purchaseInvoice {}, error {}", request.getGrnCode(), itemScanDetails.getBarcode(),
							vendorInvoiceNumber, purchaseInvoice, e.getMessage());
					itemScanDetails.setSuccess(false);
					itemScanDetails.setError(e.getMessage());
				}
				log.info("[createGrnItems] request grnCode {}, vendorInvoiceNumber {}, itemScanDetails {}",
						request.getGrnCode(), vendorInvoiceNumber, itemScanDetails);
			}
			updateBarcodeDetailsInIms(request, purchaseOrder);
			log.info("[createGrnItems] request grnCode {}, vendorInvoiceNumber {}",
					request.getGrnCode(), vendorInvoiceNumber);
		} catch (Exception e) {
			log.error("[transferBarcodeExpiry] Error while getting expiry date" + e);
			throw new ApplicationException("Exception from unicom " + e.getMessage(),
					GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	@Transactional(rollbackFor = Exception.class)
	private void createGrnItem(AutoGrnScanDetails request, AutoGrnItemScanDetails itemScanDetails,
			PurchaseInvoiceEntity purchaseInvoice, PurchaseOrder purchaseOrder) throws ApplicationException {
		String grnCode = request.getGrnCode();
		String barcode = itemScanDetails.getBarcode();
		try {
			log.info("[createGrnItem] grnCode {}, barcode {}, itemScanDetails : {}", grnCode, barcode, itemScanDetails);
			if (itemScanDetails.getExpiryDate() != null) {
				log.info("[createGrnItem] grnCode {}, barcode {}, expiry date : {}", grnCode, barcode, itemScanDetails.getExpiryDate());
				LocalDateTime localDateTime = itemScanDetails.getExpiryDate().toLocalDateTime();
				log.info("[createGrnItem] grnCode {}, barcode {}, expiry date : {}, localDateTime {}",
						grnCode, barcode, itemScanDetails.getExpiryDate(), localDateTime);
				validateExpiryDateOnProcurementType("BULK_SO_GRN", localDateTime, purchaseOrder.getProcurementYype());
				log.info("[createGrnItem] grnCode {}, barcode {}, expiry date : {}, localDateTime {} Date validated",
						grnCode, barcode, itemScanDetails.getExpiryDate(), localDateTime);
			}

			log.info("[createGrnItem] Expiry Date validated grnCode {}, barcode {}, itemScanDetails : {} -- po : {}",
					grnCode, barcode, itemScanDetails, purchaseOrder.toString());
			GRNScanItemRequest grnScanItemRequest = createGRNScanItemRequest(request, itemScanDetails, purchaseInvoice.getVendorId());
			GRNItem grnItem = createGrnItem(grnScanItemRequest, purchaseInvoice);
			grnItem.setLegalOwner(request.getLegalOwner() == null ? purchaseInvoice.getLegalOwner() : request.getLegalOwner());

			log.info("[createGrnItem] Add Details in GRN pid master grnCode {}, barcode {}", grnCode, barcode);
			addGrnPidMaster(grnItem, purchaseInvoice);
			log.info("[createGrnItem] Successfully created grnCode {}, barcode {} GRN pid master {}",
					grnCode, barcode, itemScanDetails.getBarcode());

			GrnItemEntity grnItemEntity = grnItemEntityService.findByBarcodeAndPoId(grnItem.getBarcode(), grnItem.getPoId());
			if (request.getCreatePutaway()) {
				log.info("[createGrnItem] Creating putaway request grnCode {}, barcode {}", grnCode, barcode);
				CreatePutAwayRequest putawayRequest = createPutAwayRequest(grnItem);
				log.info("[createGrnItem] Creating putaway request grnCode {}, barcode {}, putawayRequest {}",
						grnCode, barcode, putawayRequest);
				if ((grnItemEntity == null && itemScanDetails.getPutawayCode() == null) ||
					(grnItemEntity != null && grnItemEntity.getPutawayCode() == null)) {
					log.info("[createGrnItem] Create putaway  grnCode {}, barcode {}", grnCode, barcode);
					List<CreatePutawayResponse> response = putawayService.createPutaway(itemScanDetails.getBarcode(), putawayRequest);
					log.info("[createGrnItem] Putaway created grnCode {}, barcode {}, response {}",
							grnCode, barcode, response);
					String putawayCode = validateAndReturnCreatePutawayResponse(itemScanDetails.getBarcode(), response);
					log.info("[createGrnItem] Putaway created and validated grnCode {}, barcode {}, putawayCode {}",
							grnCode, barcode, putawayCode);
					itemScanDetails.setPutawayCode(putawayCode);
					grnItem.setPutawayCode(Integer.parseInt(putawayCode));
					log.info("[createGrnItem] PutawayCode Stored successfully {}, putawayCode {}",
							itemScanDetails.getBarcode(), putawayCode);
				}
			} else {
				log.info("[createGrnItem] Putaway created already created for grnCode {}, barcode {}, putaway code {} --" +
					" putaway required : {}", grnCode, barcode, itemScanDetails.getPutawayCode(), request.getCreatePutaway());
			}
			log.info("[createGrnItem] Save grn item grnCode {}, barcode {}", grnCode, barcode);
			if (grnItemEntity == null)
				grnItemEntity = grnItemDAO.saveGrnItemDetails(grnItem);
			else {
				if (request.getCreatePutaway())
					itemScanDetails.setPutawayCode(String.valueOf(grnItemEntity.getPutawayCode()));
			}
			log.info("[createGrnItem] Save grn item saved grnCode {}, barcode {}", grnCode, barcode);
			String imsOperation = itemScanDetails.getImsOperation();
			if (StringUtils.isBlank(imsOperation)) {
				imsOperation = getIMSOperation(purchaseOrder);
			}
			log.info("[createGrnItem] Storing details in ims barcode {}, GRN_QC_PASS_IN_TRANSIT -- itemScanDetails.getImsOperation() : {} " +
					"-- purchaseOrder.getProcurementYype() : {}", itemScanDetails.getBarcode(),
					imsOperation, purchaseOrder.getProcurementYype());
			log.info("[createGrnItem] Details stored in IMS {} and Item successfully added", itemScanDetails.getBarcode());
		} catch (Exception e) {
			log.error("[createGrnItem] Error while creating Grn item {}, error {} ",
					itemScanDetails.getBarcode(), e.getMessage());
			throw new ApplicationException("Error while creating Grn item: " + e.getMessage(),
					GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	private void updateBarcodeDetailsInIms(AutoGrnScanDetails request, PurchaseOrder purchaseOrder) {
		String imsOperation = request.getGrnItemDetailList().get(0).getImsOperation();
		if (StringUtils.isBlank(imsOperation)) {
			imsOperation = getIMSOperation(purchaseOrder);
		}
		log.info("[createGrnItem] Storing details in ims barcode {}, itemScanDetails.getImsOperation() : {}, " +
				"purchaseOrder.getProcurementYype() : {}", request.getGrnCode(),
				imsOperation, purchaseOrder.getProcurementYype());
		Map<String,String> barcodeUrlMap = new HashMap<>();
		prepareBarcodeUrlMap(request.getGrnItemDetailList(), barcodeUrlMap);
		updateBarcodeDetailsInIms(request.getGrnCode(), imsOperation, request.getFacilityCode(), purchaseOrder, barcodeUrlMap);
		log.info("[createGrnItem] Details stored in IMS {} and Item successfully added", request.getGrnCode());
	}

	private void prepareBarcodeUrlMap(List<AutoGrnItemScanDetails> grnItemDetailList, Map<String, String> barcodeUrlMap) {
		//preparing map for omni url for barcodes
		for(AutoGrnItemScanDetails itemScanDetails : grnItemDetailList) {
			if(StringUtils.isNotBlank(itemScanDetails.getBarcodeUrl())) {
				barcodeUrlMap.put(itemScanDetails.getBarcode(), itemScanDetails.getBarcodeUrl());
			}
		}
	}

	private void updateBarcodeDetailsInIms(String grnCode, String operation, String facilityCode, PurchaseOrder purchaseOrder, Map<String, String> barcodeUrlMap)
			throws ResponseStatusException {
		try {
			List<GrnItemEntity> grnEntities = grnItemEntityService.findByGrnCode(grnCode);
			log.info("[updateBarcodeDetailsInIms] Send GRN item details in IMS, grnCode {}, action {}, operation {}, facility {}, poType {}", 
					grnCode, ActionStatus.IMS_INSERT_GRN_ITEMS.getName(), operation, facilityCode, purchaseOrder.getPoType());
			Map<String, BarcodePriceResponse> barcodePriceMap = null;
			if (StringUtils.equalsIgnoreCase(purchaseOrder.getPoType(), "TRANSFER")) {
				barcodePriceMap = getBarcodePriceMap(grnCode, purchaseOrder, grnEntities);
			}
			UpdateStockInwardRequest updateStockInwardRequest = imsService.createUpdateStockInwardRequest(grnEntities, operation, 
						barcodePriceMap, purchaseOrder.getCurrency(), barcodeUrlMap);
			UpdateStocksResponseV2 updateStocksResponseV2 = imsService.performStockInward(updateStockInwardRequest);
			log.info("[updateBarcodeDetailsInIms] Sent GRN item details in IMS, grnCode {}, updateStockInwardRequest {}, updateStocksResponseV2 {}",
						grnCode, updateStockInwardRequest, updateStocksResponseV2);
		} catch (Exception e) {
			log.error("[updateBarcodeDetailsInIms] Unable to store details in IMS {}, {}", grnCode, e.getMessage());
			throw new ResponseStatusException(HttpStatus.BAD_REQUEST, e.getMessage());
		}
	}

	private String getIMSOperation(PurchaseOrder purchaseOrder) {
		String imsOperation = GRN_QC_PASS;
		if (StringUtils.equalsIgnoreCase("BULK_SO", purchaseOrder.getProcurementYype())) {
			if (StringUtils.equalsIgnoreCase(purchaseOrder.getPoType(), BULK)) {
				imsOperation = GRN_QC_PASS_IN_TRANSIT;
			} else if (StringUtils.equalsIgnoreCase(purchaseOrder.getPoType(), TRANSFER_TYPE)) {
				imsOperation = TRANSFER_IN_TRANSIT;
			}
		}
		return imsOperation;
	}

	private GRNScanItemRequest createGRNScanItemRequest(AutoGrnScanDetails autoGrnScanDetails,
														AutoGrnItemScanDetails itemScanDetails, String vendorId) {
		GRNScanItemRequest request = new GRNScanItemRequest();
		request.setVendorInvoiceNumber(autoGrnScanDetails.getVendorInvoiceNumber());
		request.setGrnCode(autoGrnScanDetails.getGrnCode());
		request.setBarcode(itemScanDetails.getBarcode());
		request.setProductId(itemScanDetails.getProductId());
		request.setFacilityCode(autoGrnScanDetails.getFacilityCode());
		request.setExpiryDate(itemScanDetails.getExpiryDate());
		if (StringUtils.equalsIgnoreCase(itemScanDetails.getCondition(), "BAD")) {
			request.setQcStatus("fail");
			request.setQcFailCode("400");
			request.setQcReason("Inventory marked BAD at GRN");
		} else {
			request.setQcStatus("pass");
			request.setQcFailCode("");
			request.setQcReason("");
		}
		request.setCreatedBy(autoGrnScanDetails.getCreatedBy());
		request.setVendorCode(vendorId);
		log.info("[createGRNScanItemRequest] request {}", request);
		return request;
	}

	private CreatePutAwayRequest createPutAwayRequest(GRNItem grnItem) {
		try {
			log.info("[createCreatePutAwayRequest] Create putaway request {}", grnItem.getBarcode());
			List<GRNItem> grnItemList = new ArrayList<>();
			grnItemList.add(grnItem);
			return putawayService.buildItemCreatePutawayRequestModel(grnItemList, null,
					PutAwayAction.create, "BULK_SO_USER");
		} catch (Exception e) {
			log.error("[createCreatePutAwayRequest] Error while creating putaway request item {}, error {} ",
					grnItem.getBarcode(), e.getMessage());
			throw new ApplicationException(", Error while creating putaway request " + e.getMessage(),
					GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
	}

	private String validateAndReturnCreatePutawayResponse(String barcode, List<CreatePutawayResponse> response) {
		try {
			log.info("[validateAndReturnCreatePutawayResponse] barcode {}, response {}", barcode, response);
			for (CreatePutawayResponse createPutawayResponse : response) {
				for (CreatePutawayItemResponse itemResponse : createPutawayResponse.getBarcodeInfo()) {
					if (itemResponse.getBarcodeNumber().equalsIgnoreCase(barcode) && itemResponse.getSuccess())
						return itemResponse.getNewPutawayId();
					else
						throw new ApplicationException("Putaway not created for Barcode " + barcode, GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
				}
			}
		} catch (Exception e) {
			log.error("[validateAndReturnCreatePutawayResponse] Error while validating putaway response item {}, error {} ",
					barcode, e.getMessage());
			throw new ApplicationException(e.getMessage(), GRNExceptionStatus.GRN_INTERNAL_SERVER_ERROR);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	public ResponseDTO checkBarcodeInwardValidity(String barcode, String facility, String type) {
		ResponseDTO<Map<String, Object>> responseDTO = new ResponseDTO<Map<String, Object>>();
		Map<String, Object> response = new HashMap<>();
		try {
			log.info("[checkBarcodeInwardValidity] barcode {}, facility {}, type {}", barcode, facility, type);
			boolean barcodeSuccess = barcodeValidation.validateBarcodeSuccess(barcode, facility, type, null,
					GRNConstants.GRN_QC_PASS, null);
			log.info("[checkBarcodeInwardValidity] barcode {}, facility {}, type {}, barcodeSuccess {}",
					barcode, facility, type, barcodeSuccess);
			response.put("success", barcodeSuccess);
			responseDTO.setResult(response);
		} catch (Exception e) {
			log.error("[checkBarcodeInwardValidity] barcode {}, facility {}, error {}",
					barcode, facility, e.getMessage());
			response.put("success", false);
			responseDTO.setResult(response);
			responseDTO.setDisplayMessage(e.getMessage());
		}
		log.info("[checkBarcodeInwardValidity] barcode {}, facility {}, type {}, responseDTO {}",
				barcode, facility, type, responseDTO);
		return responseDTO;
	}

	private void updateBarcodePriceInResponse(List<BarcodePriceResponse> barcodePriceResponseList,
			TaxInvoiceDetailRequestModel createTaxInvoiceRequest, String operatingCurrency) throws Exception {
		Map<String, Double> totalTaxPcForPid = getTotalTaxPcForPid(createTaxInvoiceRequest, operatingCurrency);
		for (BarcodePriceResponse barcodePriceResponse : barcodePriceResponseList) {
			String pid = barcodePriceResponse.getPid();
			Double unitPrice = barcodePriceResponse.getPrice();
			Double taxPcForPid = totalTaxPcForPid.get(pid);
			log.info("[updateBarcodePriceInResponse] taxEnabledCurrencies: {}, operatingCurrency {}, taxPcForPid {}",
					taxEnabledCurrencies, operatingCurrency, taxPcForPid);
			if (taxPcForPid != null) {
				double unitPriceWithTax = unitPrice + (unitPrice * taxPcForPid / 100);
				unitPriceWithTax = Math.round(unitPriceWithTax * 10000.0) / 10000.0;
				barcodePriceResponse.setItemPriceWithTax(unitPriceWithTax);
			}
			barcodePriceResponse.setCurrencyCode(operatingCurrency);
		}
		log.info("[updateBarcodePriceInResponse] Tax details for the barcodePriceResponseList : {}",
				barcodePriceResponseList);
	}

	private Map<String, Double> getTotalTaxPcForPid(TaxInvoiceDetailRequestModel createTaxInvoiceRequest,
			String operatingCurrency) {
		Map<String, Double> totalTaxPcForPid = new HashMap<>();
		try {
			if (taxEnabledCurrencies.contains("ALL") || taxEnabledCurrencies.contains(operatingCurrency)) {
				log.info("[getTotalTaxPcForPid] Fetching tax details for request {}", createTaxInvoiceRequest);
				List<PidTaxDetail> taxDetails = orderOpsConnector.getPidTaxDetailsResponse(createTaxInvoiceRequest);
				log.info("[getTotalTaxPcForPid] Tax details for request {} : {}", createTaxInvoiceRequest, taxDetails);
				for (PidTaxDetail pidTaxDetail : taxDetails) {
					double totalTaxPc = pidTaxDetail.taxDetails.stream().mapToDouble(t -> t.taxPercentage).sum();
					totalTaxPcForPid.put(String.valueOf(pidTaxDetail.getProductId()), totalTaxPc);
				}
			}
		} catch (Exception ex) {
			log.error("[getTotalTaxPcForPid] Exceotion caught for request : {}", createTaxInvoiceRequest, ex);
		}
		log.info("[getTotalTaxPcForPid] Total tax for pids : {}", totalTaxPcForPid);
		return totalTaxPcForPid;
	}

	private TaxInvoiceDetailRequestModel getTaxInvoiceDetailRequestModel(
			FacilityDetailsResponse facilityDetailsResponse, FacilityDetailsResponse sourceDetailsResponse,
			LegalOwner facilityLegalOwnerDetails, LegalOwner sourceLegalOwnerDetails) {
		TaxInvoiceDetailRequestModel createTaxInvoiceRequest = new TaxInvoiceDetailRequestModel();
		createTaxInvoiceRequest.setSourceCountryCode(facilityLegalOwnerDetails.getCountryCode());
		createTaxInvoiceRequest.setDestinationCountryCode(sourceLegalOwnerDetails.getCountryCode());
		createTaxInvoiceRequest
				.setSourcePincode(Math.toIntExact(facilityDetailsResponse.getBillingAddress().getPinCode()));
		createTaxInvoiceRequest
				.setDestinationPincode(Math.toIntExact(sourceDetailsResponse.getBillingAddress().getPinCode()));
		createTaxInvoiceRequest.setProductDetails(new ArrayList<>());
		return createTaxInvoiceRequest;
	}

	private ProductDetailModel updateProductDetailModels(double unitPrice, String pid) {
		ProductDetailModel productDetail = new ProductDetailModel();
		productDetail.setProductId(Integer.valueOf(pid));
		productDetail.setQuantity(1);
		if (unitPrice > 0) {
			unitPrice = Math.round(unitPrice * 10000.0) / 10000.0;
		}
		productDetail.setTotalPriceWithTax(unitPrice);
		return productDetail;
	}
}