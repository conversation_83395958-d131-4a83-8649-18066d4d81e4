<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.3.0.RELEASE</version>
		<relativePath /> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.lenskart.nexs</groupId>
	<artifactId>nexs-grn</artifactId>
	<version>0.0.2-SNAPSHOT</version>
	<name>nexs-grn</name>
	<description>Project for grn processing</description>

	<properties>
		<java.version>1.8</java.version>
		<spring-cloud.version>Hoxton.BUILD-SNAPSHOT</spring-cloud.version>
		<sonar.qualitygate.wait>false</sonar.qualitygate.wait>
		<log4j2.version>2.17.1</log4j2.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>org.springframework.boot</groupId>-->
<!--			<artifactId>spring-boot-starter-validation</artifactId>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>org.springframework.boot</groupId>-->
<!--			<artifactId>spring-boot-starter-jdbc</artifactId>-->
<!--			<version>2.5.5</version>-->
<!--		</dependency>-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>com.lenskart.nexs</groupId>
			<artifactId>redis-commons</artifactId>
			<version>1.0.1-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.lenskart.nexs</groupId>
			<artifactId>nexs-putaway-common</artifactId>
			<version>1.0.2</version>
		</dependency>
		<dependency>
			<groupId>com.lenskart.nexs</groupId>
			<artifactId>common-logger</artifactId>
			<version>1.4-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.cloud</groupId>
					<artifactId>spring-cloud-starter-sleuth</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-sleuth</artifactId>
			<version>2.2.2.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>com.lenskart.nexs</groupId>
			<artifactId>common-http</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.lenskart.nexs</groupId>
			<artifactId>nexs-pdf-generator</artifactId>
			<version>1.0-RELEASE</version>
		</dependency>
		<dependency>
			<groupId>com.lenskart.nexs</groupId>
			<artifactId>kafka-commons</artifactId>
			<version>1.0.1-RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.junit.vintage</groupId>
					<artifactId>junit-vintage-engine</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-engine</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-api</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.dataformat</groupId>
			<artifactId>jackson-dataformat-csv</artifactId>
		</dependency>
		<dependency>
			<groupId>org.jmockit</groupId>
			<artifactId>jmockit</artifactId>
			<version>1.24</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-boot-starter</artifactId>
			<version>3.0.0</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct-jdk8</artifactId>
			<version>1.3.1.Final</version>
		</dependency>
		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct-processor</artifactId>
			<version>1.3.1.Final</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.apache.commons/commons-csv -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-csv</artifactId>
			<version>1.8</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>com.lenskart.nexs</groupId>
			<artifactId>central-auth-service</artifactId>
			<version>3.0.5</version>
		</dependency>
<!--		Dependency to send email -->
		<dependency>
			<groupId>com.sendgrid</groupId>
			<artifactId>sendgrid-java</artifactId>
			<version>2.2.2</version>
		</dependency>
		<dependency>
			<groupId>com.lenskart.communication</groupId>
			<artifactId>communication-client</artifactId>
			<version>6.1.11</version>
		</dependency>
		<dependency>
			<groupId>net.javacrumbs.shedlock</groupId>
			<artifactId>shedlock-spring</artifactId>
			<version>2.2.0</version>
		</dependency>
		<dependency>
			<groupId>net.javacrumbs.shedlock</groupId>
			<artifactId>shedlock-provider-jdbc-template</artifactId>
			<version>2.1.0</version>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-envers</artifactId>
		</dependency>
		<dependency>
			<groupId>com.lenskart.nexs</groupId>
			<artifactId>nexs-ims-commons</artifactId>
			<version>1.16.15</version>
			<exclusions>
				<exclusion>
					<groupId>com.lenskart.nexs</groupId>
					<artifactId>es-restclient-commons</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.lenskart.nexs</groupId>
			<artifactId>nexs-ems-common-model</artifactId>
			<version>1.0.36</version>
			<exclusions>
				<exclusion>
					<groupId>com.lenskart.nexs</groupId>
					<artifactId>nexs-wms-common-entities</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.lenskart.nexs</groupId>
			<artifactId>inventory-bulk-transfer-common-model</artifactId>
			<version>1.0.7</version>
		</dependency>
		<dependency>
			<groupId>com.lenskart.nexs</groupId>
			<artifactId>nexs-po-common-enums</artifactId>
			<version>1.6.1</version>
		</dependency>
		<dependency>
			<groupId>com.lenskart</groupId>
			<artifactId>inventory-adapter-client</artifactId>
			<version>8.0.10</version>
		</dependency>
		<dependency>
			<groupId>com.lenskart.nexs</groupId>
			<artifactId>nexs-consul-commons</artifactId>
			<version>1.0.7-RELEASE</version>
		</dependency>
		<dependency>
			<groupId>com.lenskart.nexs</groupId>
			<artifactId>nexs-po-common-serviceutil</artifactId>
			<version>3.0.5</version>
		</dependency>
		<dependency>
			<groupId>com.lenskart.nexs</groupId>
			<artifactId>nexs-po-common-entity</artifactId>
			<version>5.0.9</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>com.lenskart.platform.fl</groupId>
			<artifactId>fl-utils</artifactId>
			<version>5.25_Spring_Boot_2.3.7.RELEASE_1.8_JDK</version>
		</dependency>
	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>Hoxton.RELEASE</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-bom</artifactId>
				<version>2.17.1</version>
				<scope>import</scope>
				<type>pom</type>
			</dependency>

		</dependencies>
	</dependencyManagement>

	<repositories>
		<repository>
			<id>nexs-releases</id>
			<url>http://localhost:7001/repository/internal/nexs-releases</url>
		</repository>
		<repository>
			<id>archiva.default</id>
			<url>http://localhost:7001/repository/internal</url>
		</repository>
		<repository>
			<id>fl-utils</id>
			<url>http://archiva-new.prod.internal:8080/repository/internal/</url>
		</repository>
	</repositories>

	<!--<distributionManagement>
		<repository>
			<id>nexs-releases</id>
			<url>http://**************:8080/repository/internal/nexs-releases</url>
		</repository>
	</distributionManagement>-->

	<build>
		<plugins>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>0.8.6</version>
				<executions>
					<execution>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<execution>
						<id>jacoco-report</id>
						<phase>test</phase>
						<goals>
							<goal>report</goal>
						</goals>
					</execution>
					<execution>
						<id>jacoco-check</id>
						<goals>
							<goal>check</goal>
						</goals>
						<configuration>
							<rules>
								<rule>
									<element>PACKAGE</element>
									<limits>
										<limit>
											<counter>LINE</counter>
											<value>COVEREDRATIO</value>
											<minimum>0.0</minimum>
										</limit>
									</limits>
								</rule>
							</rules>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>3.0.0-M5</version>
				<configuration>
					<forkedProcessExitTimeoutInSeconds>60</forkedProcessExitTimeoutInSeconds>
					<forkCount>1</forkCount>
				</configuration>
			</plugin>
			<plugin>
				<groupId>pl.project13.maven</groupId>
				<artifactId>git-commit-id-plugin</artifactId>
				<configuration>
					<offline>true</offline>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>com.spotify</groupId>
				<artifactId>docker-maven-plugin</artifactId>
				<version>1.2.2</version>
				<configuration>
					<imageName>928401551325.dkr.ecr.ap-southeast-1.amazonaws.com/${project.artifactId}-dev</imageName>
					<dockerDirectory>./</dockerDirectory>
					<imageTags>
						<imageTag>${git.commit.id.abbrev}</imageTag>
					</imageTags>
					<resources>
						<resource>
							<targetPath>/</targetPath>
							<directory>${project.build.directory}</directory>
							<include>${project.build.finalName}.jar</include>
						</resource>
					</resources>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
